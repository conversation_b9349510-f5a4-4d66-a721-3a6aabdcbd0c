/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2025-07-31 15:57:34
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-08-04 09:05:55
 */
const JwtService = require('../services/JwtService');

/**
 * JWT认证中间件
 * 验证请求头中的JWT令牌，并将解码后的用户信息添加到req对象
 */
const authenticateJWT = async (req, res, next) => {
  try {
    // 从请求头中提取令牌
    const token = JwtService.extractTokenFromHeader(req);
    
    if (!token) {
      return res.status(401).json({ error: '未提供认证令牌' });
    }
    
    // 验证令牌
    const decoded = JwtService.verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ error: '无效的认证令牌' });
    }
    
    // 检查令牌是否在黑名单中（已注销）
    const isBlacklisted = await JwtService.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({ error: '令牌已失效，请重新登录' });
    }
    
    // 将解码后的用户信息添加到请求对象
    req.user = decoded;
    req.token = token;
    
    next();
  } catch (error) {
    req.logger?.error(`JWT认证失败: ${error.message}`);
    res.status(401).json({ error: '认证过程中发生错误' });
  }
};

/**
 * 管理员权限验证中间件
 * 必须在authenticateJWT中间件之后使用
 */
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: '未认证的请求' });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: '需要管理员权限' });
  }
  
  next();
};

module.exports = {
  authenticateJWT,
  requireAdmin
};