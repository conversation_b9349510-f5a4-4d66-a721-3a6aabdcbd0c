const express = require('express');
const router = express.Router();
const DeployService = require('../services/DeployService');
const Project = require('../models/Project');
const BuildQueue = require('../models/BuildQueue');
const Server = require('../models/Server');
const VcsService = require('../services/VcsService');
const DeploymentHistory = require('../models/DeploymentHistory');
const path = require('path');

// 开始部署
router.post('/', async (req, res) => {
  try {
    const { projectId, branch } = req.body;
    
    if (!projectId || !branch) {
      return res.status(400).json({ error: '项目ID和分支名称为必填项' });
    }
    
    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    // 在后台开始部署
    const buildId = require('uuid').v4();
    DeployService.deploy(projectId, branch, req.io, buildId)
      .then(result => {
        req.logger.info(`部署完成: ${JSON.stringify(result)}`);

        // 发送成功事件
        req.io.emit('build-completed', {
          buildId: result.buildId,
          projectId: result.projectId,
          status: 'success',
          message: '部署成功'
        });
      })
      .catch(error => {
        req.logger.error(`部署错误: ${error.message}`);

        // 发送失败事件到前端
        req.io.emit('build-completed', {
          buildId: buildId,
          projectId: projectId,
          status: 'failed',
          message: `部署失败: ${error.message}`,
          error: {
            message: error.message,
            stack: error.stack
          }
        });

        // 发送全局错误事件
        req.io.emit('build-error', {
          buildId: buildId,
          projectId: projectId,
          message: `部署失败: ${error.message}`,
          time: new Date().toISOString()
        });
      });
    
    // 立即返回构建ID
    res.json({
      success: true,
      message: '部署已开始',
      buildId,
      project: {
        id: project.id,
        name: project.name
      },
      branch
    });
  } catch (error) {
    req.logger.error(`启动部署失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目分支
router.get('/branches/:projectId', async (req, res) => {
  try {
    // DeployService已经是实例化的对象，不需要再次实例化
    const branches = await DeployService.getBranches(req.params.projectId);
    res.json(branches);
  } catch (error) {
    req.logger.error(`获取分支列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取分支提交日志
router.get('/commits/:projectId/:branch', async (req, res) => {
  try {
    const { projectId, branch } = req.params;
    const { limit = 10 } = req.query;
    
    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    const projectDir = path.join(process.cwd(), 'data', 'projects', projectId);
    
    // VcsService已经是实例化的对象，不需要再次实例化
    // const vcsService = new VcsService();
    // 获取提交日志
    if(project.vcsType === 'git'){
      logs = await VcsService.getGitCommitLogs(projectDir, branch, parseInt(limit));
    }else{
      const credentialId = project.vcsCredentialId || null;
      logs = await VcsService.getSvnCommitLogs(projectDir, credentialId, parseInt(limit));
    }
    
    res.json(logs);
  } catch (error) {
    req.logger.error(`获取提交日志失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取两个分支或提交之间的差异日志
router.get('/diff/:projectId/:fromRef/:toRef', async (req, res) => {
  try {
    const { projectId, fromRef, toRef } = req.params;
    const { limit = 20 } = req.query;
    
    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    const projectDir = path.join(process.cwd(), 'data', 'projects', projectId);
    
    // VcsService已经是实例化的对象，不需要再次实例化
    // const vcsService = new VcsService();
    
    // 获取差异日志
    const logs = await VcsService.getGitDiffLogs(projectDir, fromRef, toRef, parseInt(limit));
    
    res.json(logs);
  } catch (error) {
    req.logger.error(`获取差异日志失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目在特定服务器上的部署历史
router.get('/history/:projectId/:serverId', async (req, res) => {
  try {
    const { projectId, serverId } = req.params;

    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }

    // 获取部署历史
    const history = await DeploymentHistory.getDeploymentHistory(projectId, serverId);
    res.json(history);
  } catch (error) {
    req.logger.error(`获取部署历史失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 还原到指定的部署历史版本
router.post('/restore', async (req, res) => {
  try {
    const { projectId, buildId } = req.body;

    if (!projectId || !buildId) {
      return res.status(400).json({ error: '项目ID、历史记录ID为必填项' });
    }

    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }

    const buildInfo = await BuildQueue.getBuildById(buildId);
    if (!buildInfo) {
      return res.status(404).json({ error: '构建任务不存在' });
    }

    buildInfo.servers.forEach(async (server) => {
      try {
        // 检查服务器是否存在
        const server = await Server.getById(server);
        if (!server) {
          return res.status(404).json({ error: '服务器不存在' });
        }
        // 开始还原部署
        const result = await DeployService.restoreDeployment(projectId, server.id, buildInfo.id, req.io);
        req.logger.info(`还原部署完成: ${JSON.stringify(result)}`);
      } catch (error) {
        req.logger.error(`还原部署失败: ${error.message}`);
      }
    });

    

    // 开始还原部署
    const result = await DeployService.restoreDeployment(projectId, serverId, historyId, req.io);

    res.json({
      success: true,
      message: result.message,
      buildId: result.buildId
    });
  } catch (error) {
    req.logger.error(`还原部署失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;