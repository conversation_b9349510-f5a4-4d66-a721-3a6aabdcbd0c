// 初始化管理员账户脚本
const User = require('../models/User');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function initAdmin() {
  console.log('=== 初始化管理员账户 ===\n');
  
  try {
    // 检查是否已有管理员
    const users = await User.getAll();
    const existingAdmins = users.filter(user => user.role === 'admin');
    
    if (existingAdmins.length > 0) {
      console.log('系统中已存在管理员账户:');
      existingAdmins.forEach(admin => {
        console.log(`- ${admin.username} (${admin.displayName || admin.username})`);
      });
      
      const confirm = await question('\n是否要创建新的管理员账户？(y/N): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('操作已取消。');
        rl.close();
        return;
      }
    }
    
    console.log('\n请输入管理员账户信息:');
    
    // 获取用户输入
    const username = await question('用户名: ');
    if (!username) {
      console.log('用户名不能为空！');
      rl.close();
      return;
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.getByUsername(username);
    if (existingUser) {
      console.log('用户名已存在！');
      rl.close();
      return;
    }
    
    const password = await question('密码: ');
    if (!password) {
      console.log('密码不能为空！');
      rl.close();
      return;
    }
    
    const email = await question('邮箱 (可选): ');
    const displayName = await question('显示名称 (可选): ');
    
    // 创建管理员账户
    console.log('\n正在创建管理员账户...');
    
    const admin = await User.create({
      username,
      password,
      email: email || undefined,
      displayName: displayName || undefined,
      role: 'admin',
      status: 'active' // 管理员直接激活
    });
    
    console.log('\n✅ 管理员账户创建成功！');
    console.log(`用户名: ${admin.username}`);
    console.log(`显示名称: ${admin.displayName || admin.username}`);
    console.log(`邮箱: ${admin.email || '未设置'}`);
    console.log(`角色: 管理员`);
    console.log(`状态: 已激活`);
    console.log(`创建时间: ${new Date(admin.createdAt).toLocaleString()}`);
    
    console.log('\n现在您可以使用这个账户登录系统并管理其他用户。');
    
  } catch (error) {
    console.error('\n❌ 创建管理员账户失败:', error.message);
  } finally {
    rl.close();
  }
}

// 运行脚本
if (require.main === module) {
  initAdmin();
}

module.exports = initAdmin;
