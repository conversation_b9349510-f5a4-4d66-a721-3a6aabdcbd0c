const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 团队数据文件路径
const TEAMS_FILE_PATH = path.join(__dirname, '../data/configs/teams.json');

class Team {
  constructor(id, name, description, ownerId, memberIds = [], createdAt = new Date().toISOString(), updatedAt = new Date().toISOString()) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.ownerId = ownerId;
    this.memberIds = memberIds;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  // 初始化团队配置文件
  static async initTeamsFile() {
    try {
      const configDir = path.dirname(TEAMS_FILE_PATH);
      await fs.ensureDir(configDir);
      if (!(await fs.pathExists(TEAMS_FILE_PATH))) {
        await fs.writeJson(TEAMS_FILE_PATH, { teams: [] }, { spaces: 2 });
      }
    } catch (error) {
      throw new Error(`初始化团队配置文件失败: ${error.message}`);
    }
  }

  // 获取所有团队
  static async getAll() {
    try {
      await this.initTeamsFile();
      const data = await fs.readJson(TEAMS_FILE_PATH);
      return data.teams;
    } catch (error) {
      throw new Error(`获取团队列表失败: ${error.message}`);
    }
  }

  // 根据ID获取团队
  static async getById(id) {
    try {
      const data = await fs.readJson(TEAMS_FILE_PATH);
      return data.teams.find(team => team.id === id) || null;
    } catch (error) {
      throw new Error(`获取团队详情失败: ${error.message}`);
    }
  }

  // 创建新团队
  static async create(teamData) {
    try {
      await this.initTeamsFile();
      const data = await fs.readJson(TEAMS_FILE_PATH);
      const id = uuidv4();

      // 检查团队名称是否已存在
      const existingTeam = data.teams.find(team => team.name === teamData.name);
      if (existingTeam) {
        throw new Error('团队名称已存在');
      }

      const team = new Team(
        id,
        teamData.name,
        teamData.description || '',
        teamData.ownerId,
        teamData.memberIds // 默认将创建者添加为成员
      );

      data.teams.push(team);
      await fs.writeJson(TEAMS_FILE_PATH, data, { spaces: 2 });

      return team;
    } catch (error) {
      throw new Error(`创建团队失败: ${error.message}`);
    }
  }

  // 更新团队
  static async update(id, teamData) {
    try {
      const data = await fs.readJson(TEAMS_FILE_PATH);
      const teamIndex = data.teams.findIndex(team => team.id === id);

      if (teamIndex === -1) {
        throw new Error('团队不存在');
      }

      const updatedTeam = {
        ...data.teams[teamIndex],
        ...teamData,
        updatedAt: new Date().toISOString()
      };

      data.teams[teamIndex] = updatedTeam;
      await fs.writeJson(TEAMS_FILE_PATH, data, { spaces: 2 });

      return updatedTeam;
    } catch (error) {
      throw new Error(`更新团队失败: ${error.message}`);
    }
  }

  // 删除团队
  static async delete(id) {
    try {
      const data = await fs.readJson(TEAMS_FILE_PATH);
      const filteredTeams = data.teams.filter(team => team.id !== id);

      await fs.writeJson(TEAMS_FILE_PATH, { teams: filteredTeams }, { spaces: 2 });

      return { success: true };
    } catch (error) {
      throw new Error(`删除团队失败: ${error.message}`);
    }
  }

  // 添加成员到团队
  static async addMember(teamId, userId) {
    try {
      const team = await this.getById(teamId);
      if (!team) {
        throw new Error('团队不存在');
      }

      // 确保memberIds是数组
      team.memberIds = team.memberIds || [];

      if ((team.memberIds || []).includes(userId)) {
        throw new Error('用户已经是团队成员');
      }

      team.memberIds.push(userId);
      await this.update(teamId, team);

      return team;
    } catch (error) {
      throw new Error(`添加团队成员失败: ${error.message}`);
    }
  }

  // 从团队移除成员
  static async removeMember(teamId, userId) {
    try {
      const team = await this.getById(teamId);
      if (!team) {
        throw new Error('团队不存在');
      }

      // 确保memberIds是数组
      team.memberIds = team.memberIds || [];

      // 不能移除团队所有者
      if (team.ownerId === userId) {
        throw new Error('不能移除团队所有者');
      }

      if (!((team.memberIds || []).includes(userId))) {
        throw new Error('用户不是团队成员');
      }

      team.memberIds = team.memberIds.filter(id => id !== userId);
      await this.update(teamId, team);

      return team;
    } catch (error) {
      throw new Error(`移除团队成员失败: ${error.message}`);
    }
  }

  // 获取用户所属的所有团队
  static async getTeamsByUserId(userId) {
    try {
      const data = await fs.readJson(TEAMS_FILE_PATH);
      return data.teams.filter(team => 
        team.ownerId === userId || (team.memberIds || []).includes(userId)
      );
    } catch (error) {
      throw new Error(`获取用户团队列表失败: ${error.message}`);
    }
  }

  // 获取团队统计信息
  static async getStats(userId = null) {
    try {
      await this.initTeamsFile();
      const data = await fs.readJson(TEAMS_FILE_PATH);
      const totalTeams = data.teams.length;
      let myTeamsCount = 0;

      // 如果提供了用户ID，计算该用户所属的团队数量
      if (userId) {
        myTeamsCount = data.teams.filter(team => 
          team.ownerId === userId || (team.memberIds || []).includes(userId)
        ).length;
      }

      // 导入Project模型获取项目统计
      const Project = require('./Project');
      const allProjects = await Project.getAll();
      const totalProjects = allProjects.length;
      const activeProjects = allProjects.filter(project => 
        project.status === 'active' || project.lastDeployedAt >= new Date(Date.now() - 30*24*60*60*1000).toISOString()
      ).length;

      return {
        totalTeams,
        myTeamsCount,
        totalProjects,
        activeProjects
      };
    } catch (error) {
      throw new Error(`获取团队统计信息失败: ${error.message}`);
    }
  }

  // 获取团队成员
  static async getMembers(teamId) {
    try {
      const team = await this.getById(teamId);
      if (!team) {
        throw new Error('团队不存在');
      }

      // 导入User模型
      const User = require('./User');

      // 获取所有成员(包括所有者)
      const memberIds = [...new Set([team.ownerId, ...(team.memberIds || [])])];
      const members = [];

      for (const id of memberIds) {
        const user = await User.getById(id);
        if (user) {
          members.push({
            userId: user.id,
            user: {
              id: user.id,
              name: user.displayName,
              email: user.email
            },
            role: team.ownerId === id ? 'owner' : 'member',
            joinedAt: team.createdAt
          });
        }
      }

      return members;
    } catch (error) {
      throw new Error(`获取团队成员失败: ${error.message}`);
    }
  }
}

module.exports = Team;