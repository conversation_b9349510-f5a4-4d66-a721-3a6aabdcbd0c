/* 注册页面现代化样式 */
:root {
  --primary-color: #4e73df;
  --secondary-color: #6f42c1;
  --success-color: #1cc88a;
  --accent-color: #36b9cc;
  --danger-color: #e74a3b;
  --warning-color: #f6c23e;
  --light-color: #f8f9fc;
  --dark-color: #5a5c69;
  --text-color: #333;
  --text-muted: #6c757d;
  --border-radius: 10px;
  --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
}

body {
  background: linear-gradient(135deg, #f8f9fc 0%, #e8f0fe 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
}

body.loaded {
  opacity: 1;
}

/* 背景动画 */
@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

body:before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(-45deg, rgba(78, 115, 223, 0.05), rgba(111, 66, 193, 0.05), rgba(54, 185, 204, 0.05));
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: -1;
}

/* 注册表单容器 */
.form-register {
  width: 100%;
  max-width: 450px;
  padding: 15px;
  margin: auto;
  animation: fadeIn 0.8s ease-in-out 0.3s forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: all var(--transition-speed) ease;
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

.card:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.card-body {
  padding: 2.5rem;
}

/* 表单元素样式 */
.form-control {
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  transition: all var(--transition-speed) ease;
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.form-label i {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.form-text {
  color: var(--text-muted);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
}

.btn-primary:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease-out, height 0.6s ease-out;
}

.btn-primary:active:before {
  width: 300px;
  height: 300px;
}

/* Logo 样式 */
.register-logo {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 警告提示样式 */
.alert-danger {
  border-radius: 8px;
  border-left: 4px solid var(--danger-color);
  background-color: rgba(231, 74, 59, 0.1);
  padding: 1rem;
  display: flex;
  align-items: center;
}

.alert-danger i {
  margin-right: 0.5rem;
  color: var(--danger-color);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
  position: relative;
}

a:hover {
  color: var(--secondary-color);
}

a:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  transition: width var(--transition-speed) ease;
}

a:hover:after {
  width: 100%;
}

/* 页脚样式 */
.footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.85rem;
  color: var(--text-muted);
  animation: fadeIn 0.8s ease-in-out 0.6s forwards;
  opacity: 0;
}

/* 输入框聚焦效果 */
.form-group.focused label {
  color: var(--primary-color);
  font-weight: 500;
}

/* 密码强度指示器 */
.password-strength {
  height: 5px;
  border-radius: 5px;
  margin-top: 0.5rem;
  background-color: #e9ecef;
  overflow: hidden;
  transition: all var(--transition-speed) ease;
}

.password-strength-bar {
  height: 100%;
  width: 0;
  transition: all var(--transition-speed) ease;
  border-radius: 5px;
}

.strength-weak {
  width: 25%;
  background-color: var(--danger-color);
}

.strength-medium {
  width: 50%;
  background-color: var(--warning-color);
}

.strength-strong {
  width: 75%;
  background-color: var(--accent-color);
}

.strength-very-strong {
  width: 100%;
  background-color: var(--success-color);
}

.password-strength-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  text-align: right;
  color: var(--text-muted);
}

/* 响应式调整 */
@media (max-width: 576px) {
  .card-body {
    padding: 1.5rem;
  }
  
  .register-logo {
    font-size: 2.5rem;
  }
  
  .form-register {
    padding: 10px;
  }
}

/* 加载动画 */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(78, 115, 223, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loader-text {
  margin-top: 1rem;
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* 密码可见性切换 */
.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: color var(--transition-speed) ease;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.password-field-wrapper {
  position: relative;
}

/* 表单验证样式 */
.is-valid {
  border-color: var(--success-color) !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%231cc88a' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right calc(0.375em + 0.1875rem) center !important;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

.is-invalid {
  border-color: var(--danger-color) !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e74a3b'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e74a3b' stroke='none'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right calc(0.375em + 0.1875rem) center !important;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: var(--danger-color);
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback {
  display: block;
}

/* 表单组动画 */
.form-group {
  margin-bottom: 1.5rem;
  transition: transform var(--transition-speed) ease;
}

.form-group:focus-within {
  transform: translateX(5px);
}

/* 表单提交按钮加载状态 */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading .spinner-border {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -0.5rem;
  margin-left: -0.5rem;
}

.btn-loading .btn-text {
  opacity: 0;
}