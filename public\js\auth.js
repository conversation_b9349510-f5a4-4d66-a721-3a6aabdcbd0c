/**
 * 认证相关的公共方法
 */

// 认证工具类
const AuthUtils = {
  /**
   * 获取当前登录用户信息
   * @returns {Object|null} 用户信息或null（如果未登录）
   */
  getCurrentUser: function() {
    try {
      return JSON.parse(decodeURIComponent(window.atob(localStorage.getItem('u')||'')) || 'null');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  },

  /**
   * 获取JWT令牌
   * @returns {String|null} JWT令牌或null（如果未登录）
   */
  getToken: function() {
    return localStorage.getItem('token');
  },

  /**
   * 检查用户是否已登录
   * @returns {Boolean} 是否已登录
   */
  isLoggedIn: function() {
    const user = this.getCurrentUser();
    const token = this.getToken();
    return !!(user && token);
  },

  /**
   * 检查用户是否是管理员
   * @returns {Boolean} 是否是管理员
   */
  isAdmin: function() {
    const user = this.getCurrentUser();
    return user && user.role === 'admin';
  },

  /**
   * 保存用户信息和令牌到本地存储
   * @param {Object} user - 用户信息
   * @param {String} token - JWT令牌
   */
  saveUserAndToken: function(user, token) {
    localStorage.setItem('u', window.btoa(encodeURIComponent(JSON.stringify(user))));
    localStorage.setItem('token', token);
  },

  /**
   * 清除用户信息和令牌（登出）
   */
  logout: function() {
    const token = this.getToken();
    if (token) {
      // 调用登出API，将令牌添加到黑名单
      fetch('/api/users/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }).catch(error => {
        console.error('登出失败:', error);
      });
    }
    
    // 无论API调用是否成功，都清除本地存储
    this.clearAuth();
  },
  clearAuth:function(){
    localStorage.removeItem('u');
    localStorage.removeItem('token');
  },

  /**
   * 创建带有JWT令牌的请求头
   * @param {Object} headers - 原始请求头（可选）
   * @returns {Object} 带有JWT令牌的请求头
   */
  createAuthHeaders: function(headers = {}) {
    const token = this.getToken();
    const newHeaders = { ...headers };
    
    if (token) {
      newHeaders['Authorization'] = `Bearer ${token}`;
    }
    
    return newHeaders;
  },

  /**
   * 带认证信息的fetch请求
   * @param {string} url - 请求URL
   * @param {object} options - fetch选项
   * @returns {Promise} fetch请求的Promise
   */
  fetchWithAuth: function(url, options = {}) {
    const newOptions = { ...options };
    
    // 确保headers对象存在
    newOptions.headers = newOptions.headers || {};
    
    // 如果headers是Headers对象，转换为普通对象
    if (newOptions.headers instanceof Headers) {
      const headersObj = {};
      for (const [key, value] of newOptions.headers.entries()) {
        headersObj[key] = value;
      }
      newOptions.headers = headersObj;
    }
    
    // 添加Authorization头
    const token = this.getToken();
    if (token && !newOptions.headers['Authorization']) {
      newOptions.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 发送请求并处理特定状态码
    return window.fetch(url, newOptions)
      .then(response => {
        // 检查响应状态码
        if (response.status === 401) {
          // 如果是401(未授权)或500(服务器错误)，跳转到登录页面
          console.warn(`请求失败，状态码: ${response.status}，即将跳转到登录页面`);
          
          // 如果是401，可能是token过期，清除本地存储的认证信息
          if (response.status === 401) {
            this.clearAuth();
          }
          
          // 延迟跳转，给用户一些时间看到可能的错误信息
          setTimeout(() => {
            window.location.href = '/login';
          }, 100);
        }
        
        // 返回原始响应，不影响后续处理
        return response;
      })
      .catch(error => {
        // 捕获网络错误等异常
        console.error('请求失败:', error);
        
        // 重新抛出错误，不影响调用方的错误处理
        throw error;
      });
  }
};

// 如果在浏览器环境中，将AuthUtils添加到全局window对象
if (typeof window !== 'undefined') {
  window.AuthUtils = AuthUtils;
}