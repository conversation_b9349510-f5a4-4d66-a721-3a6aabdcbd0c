<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端部署工具 - 构建队列</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/style.css">
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 侧边栏 -->
      <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
        <div class="position-sticky pt-3">
          <div class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none">
            <span class="fs-4 ms-2">部署工具</span>
          </div>
          <hr>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/">
                <i class="bi bi-speedometer2 me-2"></i>
                控制台
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/#projects">
                <i class="bi bi-folder me-2"></i>
                项目管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/#servers">
                <i class="bi bi-hdd-rack me-2"></i>
                服务器管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-page="nodeVersions">
                <i class="bi bi-filetype-js me-2"></i>
                Node.js管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="/queue">
                <i class="bi bi-list-check me-2"></i>
                构建队列
              </a>
            </li>
          </ul>
          
          <hr>
          <div class="user-info px-3 pb-3 d-none" id="sidebar-user-info">
            <div class="d-flex align-items-center">
              <i class="bi bi-person-circle me-2"></i>
              <span id="sidebar-username">用户名</span>
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-2" id="logout-btn">
              <i class="bi bi-box-arrow-right"></i> 退出登录
            </button>
          </div>
        </div>
      </div>

      <!-- 主内容区 -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">构建队列</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-queue-btn">
                <i class="bi bi-arrow-clockwise"></i> 刷新
              </button>
            </div>
          </div>
        </div>

        <!-- 当前构建队列 -->
        <div class="card mb-4">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">当前构建队列</h5>
            <span class="badge bg-primary" id="active-builds-count">0</span>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>项目</th>
                    <th>分支</th>
                    <th>状态</th>
                    <th>提交人</th>
                    <th>提交时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="queue-table-body">
                  <tr>
                    <td colspan="7" class="text-center">加载中...</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 构建历史 -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">构建历史</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>项目</th>
                    <th>分支</th>
                    <th>状态</th>
                    <th>提交人</th>
                    <th>完成时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="history-table-body">
                  <tr>
                    <td colspan="7" class="text-center">加载中...</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 构建日志模态框 -->
  <div class="modal fade" id="buildLogModal" tabindex="-1" aria-labelledby="buildLogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="buildLogModalLabel">构建日志</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="build-info mb-3">
            <p><strong>项目:</strong> <span id="log-project-name"></span></p>
            <p><strong>分支:</strong> <span id="log-branch-name"></span></p>
            <p><strong>状态:</strong> <span id="log-status"></span></p>
          </div>
          <div class="log-container bg-dark text-light p-3" style="height: 400px; overflow-y: auto;">
            <pre id="build-log-content" class="mb-0"></pre>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/socket.io.min.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // 检查用户是否已登录
      if (!AuthUtils.isLoggedIn()) {
        // 未登录，重定向到登录页面
        window.location.href = '/login';
        return;
      }
        // 当前登录用户信息（从localStorage获取）
      const currentUser = AuthUtils.getCurrentUser();
      
      // 显示用户信息
      document.getElementById('sidebar-user-info').classList.remove('d-none');
      document.getElementById('sidebar-username').textContent = currentUser.displayName || currentUser.username;
      
      // 退出登录
      document.getElementById('logout-btn').addEventListener('click', () => {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        window.location.href = '/login';
      });
      
      // 初始化Socket.io连接
      const socket = io();
      
      // 发送用户连接信息
      socket.emit('user_connected', {
        userId: currentUser.id,
        username: currentUser.username
      });
      
      // 监听构建事件
      socket.on('build-queued', (build) => {
        loadQueueData();
      });
      
      socket.on('build-started', (build) => {
        loadQueueData();
      });
      
      socket.on('build-log', (log) => {
        loadQueueData();
      });
      
      // 刷新按钮
      document.getElementById('refresh-queue-btn').addEventListener('click', () => {
        loadQueueData();
        loadHistoryData();
      });
      
      // 加载队列数据
      function loadQueueData() {
        AuthUtils.fetchWithAuth('/api/queue')
          .then(response => response.json())
          .then(data => {
            const queueTableBody = document.getElementById('queue-table-body');
            document.getElementById('active-builds-count').textContent = data.filter(build => build.status === 'running').length;
            
            if (data.length === 0) {
              queueTableBody.innerHTML = `
                <tr>
                  <td colspan="7" class="text-center">当前没有构建任务</td>
                </tr>
              `;
              return;
            }
            
            queueTableBody.innerHTML = '';
            data.forEach(build => {
              const shortId = build.id.substring(0, 8);
              const statusBadge = getStatusBadge(build.status);
              const date = new Date(build.queuedAt).toLocaleString();
              
              queueTableBody.innerHTML += `
                <tr>
                  <td>${shortId}</td>
                  <td>${build.projectName}</td>
                  <td>${build.branch}</td>
                  <td>${statusBadge}</td>
                  <td>${build.username}</td>
                  <td>${date}</td>
                  <td>
                    <button class="btn btn-sm btn-outline-info view-logs" data-id="${build.id}">
                      <i class="bi bi-file-text"></i> 日志
                    </button>
                    ${build.status === 'queued' ? `
                      <button class="btn btn-sm btn-outline-danger cancel-build" data-id="${build.id}">
                        <i class="bi bi-x-circle"></i> 取消
                      </button>
                    ` : ''}
                  </td>
                </tr>
              `;
            });
            
            // 添加查看日志事件
            document.querySelectorAll('.view-logs').forEach(button => {
              button.addEventListener('click', (e) => {
                const buildId = e.target.closest('button').getAttribute('data-id');
                viewBuildLogs(buildId);
              });
            });
            
            // 添加取消构建事件
            document.querySelectorAll('.cancel-build').forEach(button => {
              button.addEventListener('click', (e) => {
                const buildId = e.target.closest('button').getAttribute('data-id');
                cancelBuild(buildId);
              });
            });
          })
          .catch(error => {
            console.error('加载队列数据失败:', error);
          });
      }
      
      // 加载历史数据
      function loadHistoryData() {
        AuthUtils.fetchWithAuth('/api/queue/history')
          .then(response => response.json())
          .then(data => {
            const historyTableBody = document.getElementById('history-table-body');
            
            if (data.length === 0) {
              historyTableBody.innerHTML = `
                <tr>
                  <td colspan="7" class="text-center">暂无构建历史</td>
                </tr>
              `;
              return;
            }
            
            historyTableBody.innerHTML = '';
            data.forEach(build => {
              const shortId = build.id.substring(0, 8);
              const statusBadge = getStatusBadge(build.status);
              const date = new Date(build.completedAt).toLocaleString();
              
              historyTableBody.innerHTML += `
                <tr>
                  <td>${shortId}</td>
                  <td>${build.projectName}</td>
                  <td>${build.branch}</td>
                  <td>${statusBadge}</td>
                  <td>${build.username}</td>
                  <td>${date}</td>
                  <td>
                    <button class="btn btn-sm btn-outline-info view-logs" data-id="${build.id}">
                      <i class="bi bi-file-text"></i> 日志
                    </button>
                  </td>
                </tr>
              `;
            });
            
            // 添加查看日志事件
            document.querySelectorAll('.view-logs').forEach(button => {
              button.addEventListener('click', (e) => {
                const buildId = e.target.closest('button').getAttribute('data-id');
                viewBuildLogs(buildId);
              });
            });
          })
          .catch(error => {
            console.error('加载历史数据失败:', error);
          });
      }
      
      // 查看构建日志
      function viewBuildLogs(buildId) {
        AuthUtils.fetchWithAuth(`/api/queue/${buildId}`)
          .then(response => response.json())
          .then(build => {
            document.getElementById('log-project-name').textContent = build.projectName;
            document.getElementById('log-branch-name').textContent = build.branch;
            document.getElementById('log-status').textContent = getStatusText(build.status);
            
            const logContent = document.getElementById('build-log-content');
            logContent.innerHTML = '';
            
            if (build.logs && build.logs.length > 0) {
              build.logs.forEach(log => {
                const time = new Date(log.time).toLocaleTimeString();
                logContent.innerHTML += `[${time}] ${log.message}\n`;
              });
            } else {
              logContent.innerHTML = '暂无日志';
            }
            
            // 显示模态框
            const buildLogModal = new bootstrap.Modal(document.getElementById('buildLogModal'));
            buildLogModal.show();
          })
          .catch(error => {
            console.error('获取构建日志失败:', error);
            alert('获取构建日志失败');
          });
      }
      
      // 取消构建
      function cancelBuild(buildId) {
        if (confirm('确定要取消此构建任务吗？')) {
          AuthUtils.fetchWithAuth(`/api/queue/${buildId}`, {
            method: 'DELETE'
          })
            .then(response => response.json())
            .then(data => {
              loadQueueData();
              loadHistoryData();
            })
            .catch(error => {
              console.error('取消构建失败:', error);
              alert('取消构建失败');
            });
        }
      }
      
      // 获取状态徽章
      function getStatusBadge(status) {
        switch (status) {
          case 'queued':
            return '<span class="badge bg-secondary">等待中</span>';
          case 'running':
            return '<span class="badge bg-primary">构建中</span>';
          case 'success':
            return '<span class="badge bg-success">成功</span>';
          case 'failed':
            return '<span class="badge bg-danger">失败</span>';
          case 'cancelled':
            return '<span class="badge bg-warning text-dark">已取消</span>';
          default:
            return '<span class="badge bg-secondary">未知</span>';
        }
      }
      
      // 获取状态文本
      function getStatusText(status) {
        switch (status) {
          case 'queued':
            return '等待中';
          case 'running':
            return '构建中';
          case 'success':
            return '构建成功';
          case 'failed':
            return '构建失败';
          case 'cancelled':
            return '已取消';
          default:
            return '未知状态';
        }
      }
      
      // 初始加载数据
      loadQueueData();
      loadHistoryData();
    });
  </script>
</body>
</html> 