// VCS凭据管理JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // 检查用户是否已登录
  if (!AuthUtils.isLoggedIn()) {
    // 未登录，重定向到登录页面
    window.location.href = '/login';
    return;
  }
  
  // 检查用户是否是管理员
  if (!AuthUtils.isAdmin()) {
    window.location.href = '/';
    return;
  }
  
  // 获取当前用户信息
  const user = AuthUtils.getCurrentUser();
  // 初始化模态框
  const addCredentialModal = new bootstrap.Modal(document.getElementById('addCredentialModal'));
  const testConnectionModal = new bootstrap.Modal(document.getElementById('testConnectionModal'));
  
  let currentTestCredentialId = null;

  // 页面加载时获取凭据列表
  loadCredentials();

  // 添加凭据按钮事件
  document.getElementById('add-credential-button').addEventListener('click', () => {
    resetCredentialForm();
    addCredentialModal.show();
  });

  // VCS类型选择变化事件
  document.getElementById('credential-type').addEventListener('change', function() {
    const emailField = document.getElementById('email-field');
    if (this.value === 'git') {
      emailField.style.display = 'block';
    } else {
      emailField.style.display = 'none';
    }
  });

  // 保存凭据按钮事件
  document.getElementById('save-credential-btn').addEventListener('click', saveCredential);

  // 测试连接按钮事件
  document.getElementById('test-connection-btn').addEventListener('click', testConnection);

  /**
   * 加载凭据列表
   */
  function loadCredentials() {
    // 显示加载状态
    document.getElementById('total-credentials').innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
    document.getElementById('git-credentials').innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
    document.getElementById('svn-credentials').innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
    
    AuthUtils.fetchWithAuth('/api/vcs-credentials')
      .then(response => response.json())
      .then(credentials => {
        displayCredentials(credentials);
        updateCredentialStats(credentials);
      })
      .catch(error => {
        console.error('加载VCS凭据失败:', error);
        showAlert('加载VCS凭据失败', 'danger');
        // 显示错误状态
        document.getElementById('total-credentials').textContent = '加载失败';
        document.getElementById('git-credentials').textContent = '加载失败';
        document.getElementById('svn-credentials').textContent = '加载失败';
      });
  }
  
  /**
   * 更新凭据统计信息
   */
  function updateCredentialStats(credentials) {
    const totalCount = credentials.length;
    const gitCount = credentials.filter(cred => cred.type === 'git').length;
    const svnCount = credentials.filter(cred => cred.type === 'svn').length;
    
    // 使用动画效果更新数字
    animateValue('total-credentials', 0, totalCount, 1000);
    animateValue('git-credentials', 0, gitCount, 1000);
    animateValue('svn-credentials', 0, svnCount, 1000);
  }
  
  /**
   * 数字动画效果
   */
  function animateValue(elementId, start, end, duration) {
    const element = document.getElementById(elementId);
    let startTimestamp = null;
    
    const step = (timestamp) => {
      if (!startTimestamp) startTimestamp = timestamp;
      const progress = Math.min((timestamp - startTimestamp) / duration, 1);
      const value = Math.floor(progress * (end - start) + start);
      element.textContent = value;
      
      if (progress < 1) {
        window.requestAnimationFrame(step);
      }
    };
    
    window.requestAnimationFrame(step);
  }

  /**
   * 显示凭据列表
   */
  function displayCredentials(credentials) {
    const container = document.getElementById('credentials-container');
    
    // 清空容器
    container.innerHTML = '';
    
    if (credentials.length === 0) {
      const emptyState = document.createElement('div');
      emptyState.className = 'text-center py-5 animate__animated animate__fadeIn';
      emptyState.innerHTML = `
        <div class="empty-state-icon mb-4">
          <i class="bi bi-key" style="font-size: 4rem; color: var(--primary-color); opacity: 0.5;"></i>
        </div>
        <h4 class="mt-3 text-muted">暂无VCS凭据</h4>
        <p class="text-muted mb-4">点击上方"添加凭据"按钮创建第一个VCS凭据</p>
        <button class="btn btn-primary" id="empty-add-btn">
          <i class="bi bi-plus-lg me-2"></i>添加凭据
        </button>
      `;
      container.appendChild(emptyState);
      
      // 添加空状态下的按钮点击事件
      document.getElementById('empty-add-btn').addEventListener('click', () => {
        resetCredentialForm();
        addCredentialModal.show();
      });
      
      return;
    }

    // 创建凭据卡片并添加动画效果
    credentials.forEach((credential, index) => {
      const typeColor = credential.type === 'git' ? 'success' : 'info';
      const typeIcon = credential.type === 'git' ? 'git' : 'archive';
      
      const card = document.createElement('div');
      card.className = 'credential-card animate__animated animate__fadeInUp';
      card.style.animationDelay = `${index * 0.1}s`;
      
      card.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
          <div class="flex-grow-1">
            <div class="d-flex align-items-center mb-3">
              <div class="icon-wrapper me-3" style="background-color: var(--${typeColor}-color); opacity: 0.2; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                <i class="bi bi-${typeIcon}" style="font-size: 1.2rem; color: var(--${typeColor}-color);"></i>
              </div>
              <div>
                <h5 class="mb-0 fw-bold">${credential.name}</h5>
                <span class="badge bg-${typeColor} credential-type-badge mt-1">${credential.type.toUpperCase()}</span>
              </div>
            </div>
            <div class="user-info p-3 mb-3 rounded">
              <p class="mb-2">
                <i class="bi bi-person me-2 text-primary"></i>
                <strong>用户名:</strong> ${credential.username}
              </p>
              ${credential.email ? `
                <p class="mb-2">
                  <i class="bi bi-envelope me-2 text-primary"></i>
                  <strong>邮箱:</strong> ${credential.email}
                </p>
              ` : ''}
              ${credential.description ? `
                <p class="mb-2">
                  <i class="bi bi-info-circle me-2 text-primary"></i>
                  <strong>描述:</strong> ${credential.description}
                </p>
              ` : ''}
              <p class="mb-0">
                <i class="bi bi-clock me-2 text-primary"></i>
                <strong>创建时间:</strong> <small>${new Date(credential.createdAt).toLocaleString()}</small>
              </p>
            </div>
          </div>
          <div class="btn-group-vertical" role="group">
            <button class="btn btn-outline-primary mb-2" onclick="testCredential('${credential.id}', '${credential.name}')">
              <i class="bi bi-wifi me-2"></i> 测试连接
            </button>
            <button class="btn btn-outline-secondary mb-2" onclick="editCredential('${credential.id}')">
              <i class="bi bi-pencil me-2"></i> 编辑凭据
            </button>
            <button class="btn btn-outline-danger" onclick="deleteCredential('${credential.id}', '${credential.name}')">
              <i class="bi bi-trash me-2"></i> 删除凭据
            </button>
          </div>
        </div>
      `;
      
      container.appendChild(card);
    });
  }

  /**
   * 重置凭据表单
   */
  function resetCredentialForm() {
    document.getElementById('add-credential-form').reset();
    document.getElementById('addCredentialModalLabel').textContent = '添加VCS凭据';
    document.getElementById('save-credential-btn').textContent = '保存凭据';
    document.getElementById('save-credential-btn').removeAttribute('data-credential-id');
    document.getElementById('email-field').style.display = 'none';
  }

  /**
   * 保存凭据
   */
  function saveCredential() {
    const name = document.getElementById('credential-name').value;
    const type = document.getElementById('credential-type').value;
    const username = document.getElementById('credential-username').value;
    const password = document.getElementById('credential-password').value;
    const email = document.getElementById('credential-email').value;
    const description = document.getElementById('credential-description').value;
    const credentialId = document.getElementById('save-credential-btn').getAttribute('data-credential-id');

    if (!name || !type || !username) {
      showAlert('凭据名称、类型和用户名为必填项', 'danger');
      return;
    }

    // 编辑模式下密码可以为空
    if (!credentialId && !password) {
      showAlert('创建凭据时密码为必填项', 'danger');
      return;
    }

    const isEdit = !!credentialId;
    const url = isEdit ? `/api/vcs-credentials/${credentialId}` : '/api/vcs-credentials';
    const method = isEdit ? 'PUT' : 'POST';

    const requestData = {
      name,
      type,
      username,
      email,
      description
    };

    // 只有在密码不为空时才包含密码字段
    if (password) {
      requestData.password = password;
    }

    AuthUtils.fetchWithAuth(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          showAlert(data.error, 'danger');
          return;
        }
        
        addCredentialModal.hide();
        loadCredentials();
        showAlert(isEdit ? 'VCS凭据更新成功' : 'VCS凭据创建成功', 'success');
      })
      .catch(error => {
        console.error(`${isEdit ? '更新' : '创建'}VCS凭据失败:`, error);
        showAlert(`${isEdit ? '更新' : '创建'}VCS凭据失败`, 'danger');
      });
  }

  /**
   * 编辑凭据
   */
  window.editCredential = function(credentialId) {
    AuthUtils.fetchWithAuth(`/api/vcs-credentials/${credentialId}`)
      .then(response => response.json())
      .then(credential => {
        if (credential.error) {
          showAlert(credential.error, 'danger');
          return;
        }

        // 填充表单
        document.getElementById('credential-name').value = credential.name;
        document.getElementById('credential-type').value = credential.type;
        document.getElementById('credential-username').value = credential.username;
        document.getElementById('credential-password').value = ''; // 出于安全考虑不显示密码
        document.getElementById('credential-email').value = credential.email || '';
        document.getElementById('credential-description').value = credential.description || '';

        // 显示邮箱字段（如果是Git）
        if (credential.type === 'git') {
          document.getElementById('email-field').style.display = 'block';
        }

        // 设置编辑模式
        document.getElementById('addCredentialModalLabel').textContent = '编辑VCS凭据';
        document.getElementById('save-credential-btn').textContent = '更新凭据';
        document.getElementById('save-credential-btn').setAttribute('data-credential-id', credentialId);

        addCredentialModal.show();
      })
      .catch(error => {
        console.error('获取VCS凭据详情失败:', error);
        showAlert('获取VCS凭据详情失败', 'danger');
      });
  };

  /**
   * 删除凭据
   */
  window.deleteCredential = function(credentialId, credentialName) {
    // 创建自定义确认对话框
    const confirmDialog = document.createElement('div');
    confirmDialog.className = 'modal fade animate__animated animate__fadeIn';
    confirmDialog.id = 'deleteConfirmModal';
    confirmDialog.setAttribute('tabindex', '-1');
    confirmDialog.setAttribute('aria-hidden', 'true');
    
    confirmDialog.innerHTML = `
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-light">
            <h5 class="modal-title">
              <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>确认删除
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-4">
            <div class="text-center mb-4">
              <div class="icon-wrapper mb-3" style="background-color: rgba(220, 53, 69, 0.1); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <i class="bi bi-trash" style="font-size: 2.5rem; color: var(--danger-color);"></i>
              </div>
              <h4>确定要删除此凭据吗？</h4>
              <p class="text-muted">您即将删除凭据 <strong class="text-danger">"${credentialName}"</strong>，此操作无法撤销。</p>
            </div>
          </div>
          <div class="modal-footer bg-light">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>取消
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
              <i class="bi bi-trash me-2"></i>确认删除
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(confirmDialog);
    
    // 创建Bootstrap模态框实例
    const modal = new bootstrap.Modal(confirmDialog);
    modal.show();
    
    // 添加确认删除按钮事件
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
      // 显示加载状态
      this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>删除中...';
      this.disabled = true;
      
      AuthUtils.fetchWithAuth(`/api/vcs-credentials/${credentialId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          modal.hide();
          // 移除模态框
          confirmDialog.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(confirmDialog);
          });
          
          loadCredentials();
          showAlert('VCS凭据删除成功', 'success');
        })
        .catch(error => {
          console.error('删除VCS凭据失败:', error);
          // 恢复按钮状态
          document.getElementById('confirmDeleteBtn').innerHTML = '<i class="bi bi-trash me-2"></i>确认删除';
          document.getElementById('confirmDeleteBtn').disabled = false;
          showAlert('删除VCS凭据失败', 'danger');
        });
    });
    
    // 模态框关闭后移除DOM
    confirmDialog.addEventListener('hidden.bs.modal', function() {
      document.body.removeChild(confirmDialog);
    });
  };

  /**
   * 测试凭据连接
   */
  window.testCredential = function(credentialId, credentialName) {
    currentTestCredentialId = credentialId;
    document.getElementById('testConnectionModalLabel').textContent = `测试连接 - ${credentialName}`;
    document.getElementById('test-repository-url').value = '';
    document.getElementById('test-result').style.display = 'none';
    testConnectionModal.show();
  };

  /**
   * 执行连接测试
   */
  function testConnection() {
    const repositoryUrl = document.getElementById('test-repository-url').value;
    const resultDiv = document.getElementById('test-result');
    const testButton = document.getElementById('test-connection-btn');

    if (!repositoryUrl) {
      showAlert('请输入仓库URL', 'danger');
      return;
    }

    if (!currentTestCredentialId) {
      showAlert('未选择测试凭据', 'danger');
      return;
    }

    // 禁用测试按钮并显示加载状态
    testButton.disabled = true;
    testButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>测试中...';
    
    // 显示加载状态
    resultDiv.innerHTML = `
      <div class="alert alert-info animate__animated animate__fadeIn">
        <div class="d-flex align-items-center">
          <div class="spinner-border spinner-border-sm me-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <div>
            <h5 class="mb-1">正在测试连接</h5>
            <p class="mb-0 small">正在尝试连接到仓库，请稍候...</p>
          </div>
        </div>
      </div>
    `;
    resultDiv.style.display = 'block';

    AuthUtils.fetchWithAuth(`/api/vcs-credentials/${currentTestCredentialId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ repositoryUrl })
    })
      .then(response => response.json())
      .then(data => {
        // 恢复按钮状态
        testButton.disabled = false;
        testButton.innerHTML = '<i class="bi bi-wifi me-2"></i>测试连接';
        
        // 清空结果区域
        resultDiv.innerHTML = '';
        
        // 创建结果元素
        const resultAlert = document.createElement('div');
        resultAlert.className = `alert ${data.success ? 'alert-success' : 'alert-danger'} animate__animated animate__fadeIn`;
        
        if (data.success) {
          resultAlert.innerHTML = `
            <div class="d-flex">
              <div class="me-3">
                <div class="icon-wrapper" style="background-color: rgba(25, 135, 84, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <i class="bi bi-check-lg" style="font-size: 1.5rem; color: var(--success-color);"></i>
                </div>
              </div>
              <div>
                <h5 class="alert-heading mb-1">连接成功</h5>
                <p class="mb-0">${data.message}</p>
              </div>
            </div>
          `;
        } else {
          resultAlert.innerHTML = `
            <div class="d-flex">
              <div class="me-3">
                <div class="icon-wrapper" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <i class="bi bi-x-lg" style="font-size: 1.5rem; color: var(--danger-color);"></i>
                </div>
              </div>
              <div>
                <h5 class="alert-heading mb-1">连接失败</h5>
                <p class="mb-0">${data.error || '未知错误'}</p>
                <div class="mt-3">
                  <button class="btn btn-sm btn-outline-danger" id="retry-test-btn">
                    <i class="bi bi-arrow-repeat me-1"></i>重试
                  </button>
                </div>
              </div>
            </div>
          `;
        }
        
        resultDiv.appendChild(resultAlert);
        
        // 添加重试按钮事件
        const retryBtn = document.getElementById('retry-test-btn');
        if (retryBtn) {
          retryBtn.addEventListener('click', testConnection);
        }
      })
      .catch(error => {
        console.error('测试VCS连接失败:', error);
        
        // 恢复按钮状态
        testButton.disabled = false;
        testButton.innerHTML = '<i class="bi bi-wifi me-2"></i>测试连接';
        
        // 清空结果区域
        resultDiv.innerHTML = '';
        
        // 创建错误结果元素
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger animate__animated animate__fadeIn';
        errorAlert.innerHTML = `
          <div class="d-flex">
            <div class="me-3">
              <div class="icon-wrapper" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                <i class="bi bi-wifi-off" style="font-size: 1.5rem; color: var(--danger-color);"></i>
              </div>
            </div>
            <div>
              <h5 class="alert-heading mb-1">网络错误</h5>
              <p class="mb-0">无法连接到服务器，请检查您的网络连接后重试。</p>
              <div class="mt-3">
                <button class="btn btn-sm btn-outline-danger" id="retry-test-btn">
                  <i class="bi bi-arrow-repeat me-1"></i>重试
                </button>
              </div>
            </div>
          </div>
        `;
        
        resultDiv.appendChild(errorAlert);
        
        // 添加重试按钮事件
        const retryBtn = document.getElementById('retry-test-btn');
        if (retryBtn) {
          retryBtn.addEventListener('click', testConnection);
        }
      });
  }

  /**
   * 显示提示信息
   */
  function showAlert(message, type = 'info') {
    const alertId = `alert-${Date.now()}`;
    
    // 创建提示元素
    const alertDiv = document.createElement('div');
    alertDiv.id = alertId;
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed animate__animated animate__fadeInRight shadow-sm`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px; border-left: 4px solid;';
    
    // 根据类型设置图标和边框颜色
    let icon = '';
    switch(type) {
      case 'success':
        icon = '<i class="bi bi-check-circle-fill me-2"></i>';
        alertDiv.style.borderLeftColor = 'var(--success-color)';
        break;
      case 'danger':
        icon = '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        alertDiv.style.borderLeftColor = 'var(--danger-color)';
        break;
      case 'warning':
        icon = '<i class="bi bi-exclamation-circle-fill me-2"></i>';
        alertDiv.style.borderLeftColor = 'var(--warning-color)';
        break;
      case 'info':
      default:
        icon = '<i class="bi bi-info-circle-fill me-2"></i>';
        alertDiv.style.borderLeftColor = 'var(--info-color)';
        break;
    }
    
    alertDiv.innerHTML = `
      ${icon}<strong>${message}</strong>
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 添加到容器
    document.body.appendChild(alertDiv);
    
    // 4秒后开始关闭动画
    setTimeout(() => {
      if (alertDiv && alertDiv.parentNode) {
        alertDiv.classList.remove('animate__fadeInRight');
        alertDiv.classList.add('animate__fadeOutRight');
        
        // 动画结束后移除元素
        alertDiv.addEventListener('animationend', () => {
          if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
          }
        });
      }
    }, 4000);
    
    // 添加点击事件，点击时立即关闭
    alertDiv.addEventListener('click', function() {
      if (this.parentNode) {
        this.classList.remove('animate__fadeInRight');
        this.classList.add('animate__fadeOutRight');
      }
    });
  }
});
