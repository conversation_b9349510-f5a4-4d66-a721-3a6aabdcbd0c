const express = require('express');
const router = express.Router();
const NodeManager = require('../services/NodeManager');

// 获取已安装的Node.js版本
router.get('/installed', async (req, res) => {
  try {
    const versions = await NodeManager.getInstalledVersions();
    res.json({ success: true, versions });
  } catch (error) {
    req.logger.error(`获取已安装的Node.js版本失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 安装指定的Node.js版本
router.post('/install', async (req, res) => {
  try {
    const { version } = req.body;
    
    if (!version) {
      return res.status(400).json({ error: 'Node.js版本为必填项' });
    }
    
    // 验证版本号格式
    if (!/^\d+\.\d+\.\d+$/.test(version)) {
      return res.status(400).json({ error: '请输入有效的版本号，例如：14.17.0' });
    }
    
    req.logger.info(`开始安装Node.js版本: ${version}`);
    const result = await NodeManager.installVersion(version);
    req.logger.info(`Node.js v${version} 安装成功`);
    
    res.json({ success: true, message: `Node.js v${version} 安装成功`, result });
  } catch (error) {
    req.logger.error(`安装Node.js版本失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 切换到指定的Node.js版本
router.post('/switch', async (req, res) => {
  try {
    const { version } = req.body;
    
    if (!version) {
      return res.status(400).json({ error: 'Node.js版本为必填项' });
    }
    
    req.logger.info(`切换到Node.js版本: ${version}`);
    const result = await NodeManager.switchVersion(version);
    
    res.json({ success: true, message: `已切换到Node.js v${version}`, result });
  } catch (error) {
    req.logger.error(`切换Node.js版本失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 