const express = require('express');
const router = express.Router();
const BuildQueue = require('../models/BuildQueue');
const DeployService = require('../services/DeployService');
const Project = require('../models/Project');
const User = require('../models/User');

// 获取当前构建队列
router.get('/', async (req, res) => {
  try {
    const queue = await BuildQueue.getQueue();
    res.json(queue);
  } catch (error) {
    req.logger.error(`获取构建队列失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取构建历史
router.get('/history', async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : 20;
    const history = await BuildQueue.getHistory(limit);
    res.json(history);
  } catch (error) {
    req.logger.error(`获取构建历史失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取构建详情
router.get('/:id', async (req, res) => {
  try {
    const build = await BuildQueue.getBuildById(req.params.id);
    if (!build) {
      return res.status(404).json({ error: '构建任务不存在' });
    }
    res.json(build);
  } catch (error) {
    req.logger.error(`获取构建详情失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取用户的构建任务
router.get('/user/:userId', async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const builds = await BuildQueue.getUserBuilds(req.params.userId, limit);
    res.json(builds);
  } catch (error) {
    req.logger.error(`获取用户构建任务失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目的构建任务
router.get('/project/:projectId', async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const builds = await BuildQueue.getProjectBuilds(req.params.projectId, limit);
    res.json(builds);
  } catch (error) {
    req.logger.error(`获取项目构建任务失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 添加构建任务到队列
router.post('/', async (req, res) => {
  try {
    const { projectId, branch, userId, username, servers } = req.body;
    
    if (!projectId || !branch || !userId || !username) {
      return res.status(400).json({ error: '项目ID、分支、用户ID和用户名为必填项' });
    }
    
    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    // 添加到队列
    const build = await BuildQueue.addToQueue({
      projectId,
      branch,
      userId,
      username,
      servers
    });
    
    // 通知客户端
    req.io.emit('build-queued', build);
    
    // 检查是否可以开始构建
    if (await BuildQueue.canStartNewBuild()) {
      // 启动构建进程
      processNextBuild(req.io, req.logger);
    }
    
    res.status(201).json(build);
  } catch (error) {
    req.logger.error(`添加构建任务失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 取消构建任务
router.delete('/:id', async (req, res) => {
  try {
    const build = await BuildQueue.cancelBuild(req.params.id);
    
    // 通知客户端
    req.io.emit('build-cancelled', build);
    
    res.json(build);
  } catch (error) {
    req.logger.error(`取消构建任务失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 处理队列中的下一个构建任务
async function processNextBuild(io, logger) {
  try {
    // 获取队列
    const queue = await BuildQueue.getQueue();
    
    // 查找下一个等待中的任务
    const nextBuild = queue.find(build => build.status === 'queued');
    if (!nextBuild) {
      return; // 没有等待中的任务
    }
    
    // 将任务标记为运行中
    await BuildQueue.updateBuildStatus(nextBuild.id, 'running', '开始构建');
    BuildQueue.addActiveBuild(nextBuild.id);
    
    // 通知客户端
    io.emit('build-started', nextBuild);
    
    // 执行部署
    try {
      await DeployService.deploy(nextBuild.projectId, nextBuild.branch, io, nextBuild.id, nextBuild.servers);
      await BuildQueue.updateBuildStatus(nextBuild.id, 'success', '构建成功');

      // 发送成功事件
      io.emit('build-completed', {
        buildId: nextBuild.id,
        projectId: nextBuild.projectId,
        status: 'success',
        message: '构建成功'
      });
    } catch (error) {
      logger.error(`构建失败: ${error.message}`);
      const errorMessage = `构建失败: ${error.message}`;

      await BuildQueue.updateBuildStatus(nextBuild.id, 'failed', errorMessage);

      // 发送失败事件到前端
      io.emit('build-completed', {
        buildId: nextBuild.id,
        projectId: nextBuild.projectId,
        status: 'failed',
        message: errorMessage,
        error: {
          message: error.message,
          stack: error.stack
        }
      });

      // 发送全局错误事件
      io.emit('build-error', {
        buildId: nextBuild.id,
        projectId: nextBuild.projectId,
        message: errorMessage,
        time: new Date().toISOString()
      });
    } finally {
      BuildQueue.removeActiveBuild(nextBuild.id);

      // 检查是否可以开始下一个构建
      if (await BuildQueue.canStartNewBuild()) {
        processNextBuild(io, logger);
      }
    }
  } catch (error) {
    logger.error(`处理构建队列失败: ${error.message}`);
  }
}

module.exports = router; 