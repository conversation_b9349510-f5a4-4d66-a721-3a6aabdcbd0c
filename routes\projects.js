const express = require('express');
const router = express.Router();
const Project = require('../models/Project');
const DeployService = require('../services/DeployService');
const ProjectServer = require('../models/ProjectServer');
const Server = require('../models/Server');
const SecurityAgent = require('../services/SecurityAgent');
const Team = require('../models/Team'); // 导入团队模型

// 获取所有项目
router.get('/', async (req, res) => {
  try {
    // 管理员可以看到所有项目
    if (req.user.role === 'admin') {
      const projects = await Project.getAll();
      res.json(projects);
    } else {
      // 普通用户只能看到自己团队的项目
      const teams = await Team.getTeamsByUserId(req.user.id);
      const teamIds = teams.map(team => team.id);
      let userProjects = [];

      for (const teamId of teamIds) {
        const projects = await Project.getByTeamId(teamId);
        userProjects = [...userProjects, ...projects];
      }

      res.json(userProjects);
    }
  } catch (error) {
    req.logger.error(`获取项目列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 根据ID获取项目
router.get('/:id', async (req, res) => {
  try {
    const project = await Project.getById(req.params.id);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }

    // 检查权限：管理员或项目所属团队的成员
    if (req.user.role !== 'admin') {
      const teams = await Team.getTeamsByUserId(req.user.id);
      const teamIds = teams.map(team => team.id);

      if (!teamIds.includes(project.teamId)) {
        return res.status(403).json({ error: '没有权限访问此项目' });
      }
    }

    // 获取项目的服务器配置
    const serverConfigs = await ProjectServer.getByProjectId(project.id);

    // 为响应添加服务器配置
    const projectWithServers = {
      ...project,
      servers: serverConfigs
    };

    res.json(projectWithServers);
  } catch (error) {
    req.logger.error(`获取项目详情失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 创建新项目
router.post('/', async (req, res) => {
  try {
    const { name, repositoryUrl, buildCommand, outputDir, servers, branches, description, nodeVersion, vcsType, packageManager, vcsCredentialId } = req.body;

    if (!name || !repositoryUrl) {
      return res.status(400).json({ error: '项目名称和Git URL为必填项' });
    }

    // 验证构建命令
    if (buildCommand && !SecurityAgent.validateBuildCommand(buildCommand)) {
      // 记录可能的注入尝试
      SecurityAgent.logInjectionAttempt(buildCommand, {
        action: 'project_create_api',
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({ 
        error: '构建命令包含不安全的内容，请使用标准的npm/yarn/pnpm命令' 
      });
    }

    const project = await Project.create({
      name,
      gitUrl: repositoryUrl,
      buildCommand,
      outputDir,
      servers,
      branches,
      description,
      nodeVersion,
      vcsType,
      packageManager,
      vcsCredentialId
    });

    res.status(201).json(project);
  } catch (error) {
    req.logger.error(`创建项目失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新项目
router.put('/:id', async (req, res) => {
  try {
    const { name, repositoryUrl, buildCommand, outputDir, servers, branches, description, nodeVersion, vcsType, packageManager, vcsCredentialId } = req.body;

    // 获取项目当前信息
    const currentProject = await Project.getById(req.params.id);
    if (!currentProject) {
      return res.status(404).json({ error: '项目不存在' });
    }
    let teamId = currentProject.teamId;

    // 验证构建命令
    if (buildCommand && !SecurityAgent.validateBuildCommand(buildCommand)) {
      // 记录可能的注入尝试
      SecurityAgent.logInjectionAttempt(buildCommand, {
        action: 'project_update_api',
        projectId: req.params.id,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({ 
        error: '构建命令包含不安全的内容，请使用标准的npm/yarn/pnpm命令' 
      });
    }

    const project = await Project.update(req.params.id, {
      name,
      gitUrl: repositoryUrl,
      buildCommand,
      outputDir,
      servers, // 这里会在Project模型中处理服务器关系
      branches,
      description,
      nodeVersion,
      vcsType,
      packageManager,
      vcsCredentialId,
      teamId
    });

    res.json(project);
  } catch (error) {
    req.logger.error(`更新项目失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 删除项目
router.delete('/:id', async (req, res) => {
  try {
    const result = await Project.delete(req.params.id);
    if (!result) {
      return res.status(404).json({ error: '项目不存在' });
    }
    res.json({ success: true });
  } catch (error) {
    req.logger.error(`删除项目失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目分支
router.get('/:id/branches', async (req, res) => {
  try {
    // DeployService已经是实例化的对象，不需要再次实例化
    const branches = await DeployService.getBranches(req.params.id);
    res.json(branches);
  } catch (error) {
    req.logger.error(`获取项目分支失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目的服务器配置
router.get('/:id/servers', async (req, res) => {
  try {
    const servers = await Server.getServersByProjectId(req.params.id);
    // 过滤敏感数据
    const safeServers = servers.map(server => {
      const { password, ...safeServer } = server;
      return safeServer;
    });
    res.json(safeServers);
  } catch (error) {
    req.logger.error(`获取项目服务器配置失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新项目的服务器配置
router.put('/:id/servers', async (req, res) => {
  try {
    const { servers } = req.body;
    
    if (!Array.isArray(servers)) {
      return res.status(400).json({ error: '服务器配置必须是数组' });
    }
    
    // 检查项目是否存在
    const project = await Project.getById(req.params.id);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    // 将服务器配置转换为需要的格式
    const serverConfigs = servers.map(config => {
      return {
        serverId: config.serverId,
        deployPath: config.deployPath || '/var/www/html'
      };
    });
    
    // 设置项目的服务器配置
    await ProjectServer.setProjectServers(req.params.id, serverConfigs);
    
    res.json({ success: true });
  } catch (error) {
    req.logger.error(`更新项目服务器配置失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;