document.addEventListener("DOMContentLoaded", function () {
  // 初始化页面
  initTeamManagement();

  // 刷新按钮点击事件
  document.getElementById("refresh-btn").addEventListener("click", function () {
    refreshTeamData();
  });

  // 创建团队按钮点击事件
  document
    .getElementById("create-team-btn")
    .addEventListener("click", function () {
      showCreateTeamModal();
    });

  // 保存团队按钮点击事件
  document
    .getElementById("save-team-btn")
    .addEventListener("click", function () {
      saveTeam();
    });

  // 标签页切换事件
  const teamTabs = document.getElementById("teamTabs");
  teamTabs.addEventListener("shown.bs.tab", function (event) {
    const tabId = event.target.getAttribute("data-bs-target");
    if (tabId === "#my-teams") {
      loadMyTeams();
    } else if (tabId === "#all-teams") {
      loadAllTeams();
    }
  });
});

// 初始化团队管理页面
function initTeamManagement() {
  // 检查用户是否已登录
  if (!AuthUtils.isLoggedIn()) {
    // 未登录，重定向到登录页面
    window.location.href = "/login";
    return;
  }

  // 检查用户是否是管理员
  if (!AuthUtils.isAdmin()) {
    window.location.href = "/";
    return;
  }
  // 加载统计数据
  loadTeamStats();
  // 加载我的团队
  loadMyTeams();
  // 加载所有团队数量
  loadAllTeamsCount();
}

// 刷新团队数据
function refreshTeamData() {
  // 显示加载状态
  showLoadingState("#my-teams-container");
  showLoadingState("#all-teams-container");

  // 重新加载数据
  loadTeamStats();
  loadMyTeams();
  loadAllTeamsCount();

  // 如果当前激活的是所有团队标签页，则重新加载所有团队
  const activeTab = document.querySelector("#teamTabs .nav-link.active");
  if (activeTab && activeTab.getAttribute("data-bs-target") === "#all-teams") {
    loadAllTeams();
  }
}

// 加载团队统计数据
function loadTeamStats() {
  fetch("/api/teams/stats", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队统计数据失败");
      }
      return response.json();
    })
    .then((data) => {
      renderTeamStats(data);
    })
    .catch((error) => {
      console.error("加载团队统计数据失败:", error);
      // 不显示错误提示，使用默认统计数据
      renderTeamStats({
        myTeamsCount: 0
      });
    });
}

// 渲染团队统计数据
function renderTeamStats(stats) {
  const statsContainer = document.getElementById("team-stats");
  statsContainer.innerHTML = "";

  const statsItems = [
    {
      icon: "bi-diagram-3",
      label: "总团队数",
      value: stats.totalTeams || 0,
    },
    {
      icon: "bi-users",
      label: "我的团队数",
      value: stats.myTeamsCount || 0,
    },
    {
      icon: "bi-boxes",
      label: "团队项目总数",
      value: stats.totalProjects || 0,
    },
    {
      icon: "bi-check-circle",
      label: "活跃项目数",
      value: stats.activeProjects || 0,
    },
  ];

  statsItems.forEach((item) => {
    const col = document.createElement("div");
    col.className = "col-md-3 col-sm-6";

    const statsItem = document.createElement("div");
    statsItem.className = "stats-item";

    const iconWrapper = document.createElement("div");
    iconWrapper.className = "icon-wrapper";
    iconWrapper.innerHTML = `<i class="bi ${item.icon}"></i>`;

    const statsInfo = document.createElement("div");
    statsInfo.className = "stats-info";
    statsInfo.innerHTML = `
      <div class="stats-value">${item.value}</div>
      <div class="stats-label">${item.label}</div>
    `;

    statsItem.appendChild(iconWrapper);
    statsItem.appendChild(statsInfo);
    col.appendChild(statsItem);
    statsContainer.appendChild(col);
  });
}

// 显示创建团队模态框
function showCreateTeamModal() {
  document.getElementById("team-name").value = "";
  document.getElementById("team-description").value = "";
  
  // 获取或创建模态框实例
  const modalElement = document.getElementById("createTeamModal");
  let createTeamModal = bootstrap.Modal.getInstance(modalElement);
  if (!createTeamModal) {
    createTeamModal = new bootstrap.Modal(modalElement);
  }
  createTeamModal.show();
}

// 保存团队
function saveTeam() {
  const name = document.getElementById("team-name").value.trim();
  const description = document.getElementById("team-description").value.trim();

  if (!name) {
    showErrorToast("团队名称不能为空");
    return;
  }

  fetch("/api/teams", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
    body: JSON.stringify({ name, description }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("创建团队失败");
      }
      return response.json();
    })
    .then((data) => {
      showSuccessToast("团队创建成功");
      const createTeamModal = bootstrap.Modal.getInstance(
        document.getElementById("createTeamModal")
      );
      createTeamModal.hide();
      refreshTeamData();
    })
    .catch((error) => {
      console.error("创建团队失败:", error);
      // 尝试获取后端返回的具体错误消息
      if (error.json) {
        error.json().then((data) => {
          showErrorToast("创建团队失败: " + (data.message || error.message));
        }).catch(() => {
          showErrorToast("创建团队失败: " + error.message);
        });
      } else {
        showErrorToast("创建团队失败: " + error.message);
      }
    });
}

// 加载我的团队
function loadMyTeams() {
  showLoadingState("#my-teams-container");

  fetch("/api/teams", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取我的团队失败");
      }
      return response.json();
    })
    .then((data) => {
      renderTeams(data, "#my-teams-container");
    })
    .catch((error) => {
      console.error("加载我的团队失败:", error);
      showErrorToast("加载我的团队失败，请重试");
      hideLoadingState("#my-teams-container");
    });
}

// 加载所有团队
function loadAllTeams() {
  showLoadingState("#all-teams-container");

  fetch("/api/teams", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取所有团队失败");
      }
      return response.json();
    })
    .then((data) => {
      renderTeams(data, "#all-teams-container");
    })
    .catch((error) => {
      console.error("加载所有团队失败:", error);
      showErrorToast("加载所有团队失败，请重试");
      hideLoadingState("#all-teams-container");
    });
}

// 加载所有团队数量
function loadAllTeamsCount() {
  fetch("/api/teams/count", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队数量失败");
      }
      return response.json();
    })
    .then((data) => {
      document.getElementById("all-teams-count").textContent = data.count || 0;
    })
    .catch((error) => {
      console.error("获取团队数量失败:", error);
      document.getElementById("all-teams-count").textContent = "0";
    });
}

// 渲染团队列表
function renderTeams(teams, containerSelector) {
  const container = document.querySelector(containerSelector);
  container.innerHTML = "";

  if (!teams || teams.length === 0) {
    container.innerHTML =
      '<div class="text-center py-5"><p>暂无团队数据</p></div>';
    hideLoadingState(containerSelector);
    return;
  }

  const table = document.createElement("table");
  table.className = "table table-striped table-hover";
  table.innerHTML = `
    <thead>
      <tr>
        <th>团队名称</th>
        <th>描述</th>
        <th>创建时间</th>
        <th>成员数量</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
    </tbody>
  `;

  const tbody = table.querySelector("tbody");

  teams.forEach((team) => {
    const row = document.createElement("tr");
    row.innerHTML = `
      <td>${team.name}</td>
      <td>${team.description || "-"}</td>
      <td>${formatDate(team.createdAt)}</td>
      <td>${team.memberIds ? team.memberIds.length : 0}</td>
      <td>
        <button class="btn btn-sm btn-primary view-team-btn" data-team-id="${
          team.id
        }">查看</button>
        <button class="btn btn-sm btn-warning edit-team-btn" data-team-id="${
          team.id
        }">编辑</button>
        <button class="btn btn-sm btn-info members-team-btn" data-team-id="${
          team.id
        }">成员管理</button>
        <button class="btn btn-sm btn-danger delete-team-btn" data-team-id="${
          team.id
        }">删除</button>
      </td>
    `;
    tbody.appendChild(row);
  });

  container.appendChild(table);
  hideLoadingState(containerSelector);

  // 绑定团队操作按钮事件
  bindTeamActionEvents(containerSelector);
}

// 绑定团队操作按钮事件
function bindTeamActionEvents(containerSelector) {
  // 查看团队按钮事件
  document
    .querySelectorAll(`${containerSelector} .view-team-btn`)
    .forEach((btn) => {
      btn.addEventListener("click", function () {
        const teamId = this.getAttribute("data-team-id");
        viewTeam(teamId);
      });
    });

  // 编辑团队按钮事件
  document
    .querySelectorAll(`${containerSelector} .edit-team-btn`)
    .forEach((btn) => {
      btn.addEventListener("click", function () {
        const teamId = this.getAttribute("data-team-id");
        editTeam(teamId);
      });
    });

  // 成员管理按钮事件
  document
    .querySelectorAll(`${containerSelector} .members-team-btn`)
    .forEach((btn) => {
      btn.addEventListener("click", function () {
        const teamId = this.getAttribute("data-team-id");
        manageTeamMembers(teamId);
      });
    });

  // 删除团队按钮事件
  document
    .querySelectorAll(`${containerSelector} .delete-team-btn`)
    .forEach((btn) => {
      btn.addEventListener("click", function () {
        const teamId = this.getAttribute("data-team-id");
        deleteTeam(teamId);
      });
    });
}

// 查看团队
function viewTeam(teamId) {
  // 显示团队详情模态框
  fetch(`/api/teams/${teamId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队信息失败");
      }
      return response.json();
    })
    .then((team) => {
      // 填充团队详情到模态框
      document.getElementById("teamDetailModalLabel").textContent = `团队详情 - ${team.name}`;
      document.getElementById("team-detail-name").textContent = team.name || '未设置';
      document.getElementById("team-detail-description").textContent = team.description || '未设置';
      document.getElementById("team-detail-createdAt").textContent = formatDate(team.createdAt);
      
      // 加载团队成员
      fetch(`/api/teams/${teamId}/members`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getAuthToken()}`,
        },
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error("获取团队成员失败");
          }
          return response.json();
        })
        .then((members) => {
          const membersList = document.getElementById("team-members-list");
          membersList.innerHTML = '';
          
          if (members.length === 0) {
            membersList.innerHTML = `<tr><td colspan="3" class="text-center text-muted">暂无团队成员</td></tr>`;
            return;
          }
          
          members.forEach((member) => {
            const row = document.createElement("tr");
            row.innerHTML = `
              <td>${member.user?.name || '未知'}</td>
              <td>${member.role === 'owner' ? '负责人' : '成员'}</td>
              <td>${formatDate(member.joinedAt)}</td>
            `;
            membersList.appendChild(row);
          });
        })
        .catch((error) => {
          console.error("加载团队成员失败:", error);
          showErrorToast("加载团队成员失败: " + error.message);
        });
      
      // 绑定添加项目按钮事件
      document.getElementById('add-project-to-team-btn').onclick = function() {
        showAddProjectToTeamModal(teamId);
      };
      
      // 标签页切换事件
      const teamDetailTabs = document.getElementById('teamDetailTabs');
      teamDetailTabs.addEventListener('shown.bs.tab', function (event) {
        const tabId = event.target.getAttribute('data-bs-target');
        if (tabId === '#projects') {
          loadTeamProjects(teamId);
        }
      });
      
      // 初始加载时，如果当前激活的是项目标签页，则加载项目数据
      const activeTab = teamDetailTabs.querySelector('.nav-link.active');
      if (activeTab && activeTab.getAttribute('data-bs-target') === '#projects') {
        loadTeamProjects(teamId);
      }
      
      // 显示模态框
      const modalElement = document.getElementById("teamDetailModal");
      let teamDetailModal = bootstrap.Modal.getInstance(modalElement);
      if (!teamDetailModal) {
        teamDetailModal = new bootstrap.Modal(modalElement);
      }
      teamDetailModal.show();
    })
    .catch((error) => {
      console.error("查看团队详情失败:", error);
      showErrorToast("查看团队详情失败: " + error.message);
    });
}

// 编辑团队
function editTeam(teamId) {
  fetch(`/api/teams/${teamId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队信息失败");
      }
      return response.json();
    })
    .then((team) => {
      document.getElementById("edit-team-id").value = team.id;
      document.getElementById("edit-team-name").value = team.name;
      document.getElementById("edit-team-description").value =
        team.description || "";
      const modalElement = document.getElementById("editTeamModal");
      let editTeamModal = bootstrap.Modal.getInstance(modalElement);
      if (!editTeamModal) {
        editTeamModal = new bootstrap.Modal(modalElement);
      }
      editTeamModal.show();
    })
    .catch((error) => {
      console.error("编辑团队失败:", error);
      showErrorToast("获取团队信息失败，请重试");
    });
}

// 保存编辑的团队
function saveEditedTeam() {
  const teamId = document.getElementById("edit-team-id").value;
  const name = document.getElementById("edit-team-name").value.trim();
  const description = document
    .getElementById("edit-team-description")
    .value.trim();

  if (!name) {
    showErrorToast("团队名称不能为空");
    return;
  }

  fetch(`/api/teams/${teamId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
    body: JSON.stringify({ name, description }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("更新团队失败");
      }
      return response.json();
    })
    .then((data) => {
      showSuccessToast("团队更新成功");
      const editTeamModal = bootstrap.Modal.getInstance(
        document.getElementById("editTeamModal")
      );
      editTeamModal.hide();
      refreshTeamData();
    })
    .catch((error) => {
      console.error("更新团队失败:", error);
      showErrorToast("更新团队失败，请重试");
    });
}

// 管理团队成员
function manageTeamMembers(teamId) {
  // 显示团队成员管理模态框
  // 首先检查模态框是否存在，如果不存在则创建
  let teamMembersModal = document.getElementById("teamMembersModal");
  if (!teamMembersModal) {
    // 创建模态框
    const modalHTML = `
      <div class="modal fade" id="teamMembersModal" tabindex="-1" aria-labelledby="teamMembersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="teamMembersModalLabel">团队成员管理</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="team-members-content">
              <!-- 成员内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
      </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    teamMembersModal = document.getElementById("teamMembersModal");
  }

  document.getElementById("teamMembersModalLabel").textContent = `团队成员管理 - 加载中...`;
  const teamMembersContent = document.getElementById("team-members-content");
  teamMembersContent.innerHTML = `
    <div class="text-center py-5 placeholder-glow">
      <div class="spinner-border text-primary mb-3" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="text-muted">正在加载团队成员数据...</p>
    </div>
  `;

  // 加载团队信息
  fetch(`/api/teams/${teamId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队信息失败");
      }
      return response.json();
    })
    .then((team) => {
      document.getElementById("teamMembersModalLabel").textContent = `团队成员管理 - ${team.name}`;

      // 加载团队成员
      return fetch(`/api/teams/${teamId}/members`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getAuthToken()}`,
        },
      });
    })
    .then((response) => {
      if (!response.ok) {
        throw new Error("获取团队成员失败");
      }
      return response.json();
    })
    .then((members) => {
      const teamMembersContent = document.getElementById("team-members-content");
      teamMembersContent.innerHTML = `
        <div class="mb-4">
          <div class="input-group">
            <input type="text" id="add-member-username" class="form-control" placeholder="输入用户名添加成员">
            <button class="btn btn-primary" id="add-member-btn" data-team-id="${teamId}">添加成员</button>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="table table-hover table-sm">
            <thead>
              <tr>
                <th scope="col">成员名称</th>
                <th scope="col">角色</th>
                <th scope="col">加入时间</th>
                <th scope="col">操作</th>
              </tr>
            </thead>
            <tbody id="team-members-list">
              ${members.map(member => `
                <tr>
                  <td>${member.user?.name || '未知'}</td>
                  <td>${member.role === 'owner' ? '负责人' : '成员'}</td>
                  <td>${formatDate(member.joinedAt)}</td>
                  <td>
                    ${member.role !== 'owner' ? `
                      <button class="btn btn-sm btn-danger remove-member-btn" data-team-id="${teamId}" data-user-id="${member.userId}">移除</button>
                    ` : '不可移除'}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;

      // 移除之前的事件监听器，避免重复绑定
      const addMemberBtn = document.getElementById("add-member-btn");
      const newAddMemberBtn = addMemberBtn.cloneNode(true);
      addMemberBtn.parentNode.replaceChild(newAddMemberBtn, addMemberBtn);
      
      // 绑定添加成员按钮事件
      newAddMemberBtn.addEventListener("click", function () {
        const username = document.getElementById("add-member-username").value.trim();
        if (!username) {
          showErrorToast("用户名不能为空");
          return;
        }

        addTeamMember(teamId, username);
      });

      // 绑定移除成员按钮事件
      document.querySelectorAll(".remove-member-btn").forEach(btn => {
        btn.addEventListener("click", function () {
          const userId = this.getAttribute("data-user-id");
          removeTeamMember(teamId, userId);
        });
      });
    })
    .catch((error) => {
      console.error("加载团队成员失败:", error);
      showErrorToast("加载团队成员失败: " + error.message);
    });

  // 显示模态框
  let modal = bootstrap.Modal.getInstance(teamMembersModal);
  if (!modal) {
    modal = new bootstrap.Modal(teamMembersModal);
    
    // 添加模态框隐藏时的清理事件
    teamMembersModal.addEventListener('hidden.bs.modal', function () {
      // 如果是动态创建的模态框，在隐藏后移除以避免内存泄漏
      if (!document.querySelector('#teamMembersModal[data-static]')) {
        teamMembersModal.remove();
      }
    }, { once: true }); // 只执行一次，避免重复绑定
  }
  modal.show();
}

// 添加团队成员
function addTeamMember(teamId, username) {
  fetch(`/api/teams/${teamId}/members`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getAuthToken()}`,
    },
    body: JSON.stringify({ username }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("添加成员失败");
      }
      return response.json();
    })
    .then((data) => {
      showSuccessToast("成员添加成功");
      // 重新加载成员列表
      manageTeamMembers(teamId);
    })
    .catch((error) => {
      console.error("添加成员失败:", error);
      // 尝试获取后端返回的具体错误消息
      if (error.json) {
        error.json().then((data) => {
          showErrorToast("添加成员失败: " + (data.message || error.message));
        }).catch(() => {
          showErrorToast("添加成员失败: " + error.message);
        });
      } else {
        showErrorToast("添加成员失败: " + error.message);
      }
    });
}

// 移除团队成员
function removeTeamMember(teamId, userId) {
  if (confirm("确定要移除这个成员吗？")) {
    fetch(`/api/teams/${teamId}/members/${userId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getAuthToken()}`,
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("移除成员失败");
        }
        return response.json();
      })
      .then((data) => {
        showSuccessToast("成员移除成功");
        // 重新加载成员列表
        manageTeamMembers(teamId);
      })
      .catch((error) => {
        console.error("移除成员失败:", error);
        showErrorToast("移除成员失败: " + error.message);
      });
  }
}

// 加载团队项目
function loadTeamProjects(teamId) {
  // const projectsContainer = document.getElementById('team-projects-container');
  // showLoadingState('#team-projects-container');

  fetch(`/api/teams/${teamId}/projects`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error('获取团队项目失败');
      }
      return response.json();
    })
    .then((projects) => {
      renderTeamProjects(projects, teamId);
    })
    .catch((error) => {
      console.error('加载团队项目失败:', error);
      showErrorToast('加载团队项目失败: ' + error.message);
      hideLoadingState('#team-projects-container');
    });
}

// 渲染团队项目
function renderTeamProjects(projects, teamId) {
  let projectsList = document.getElementById('team-projects-list');
  
  // 检查元素是否存在，如果不存在则等待一段时间后重试
  if (!projectsList) {
    setTimeout(() => renderTeamProjects(projects, teamId), 100);
    return;
  }
  
  projectsList.innerHTML = '';

  if (!projects || projects.length === 0) {
    projectsList.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无项目数据</td></tr>';
    hideLoadingState('#team-projects-container');
    return;
  }

  projects.forEach((project) => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${project.name}</td>
      <td>${project.gitUrl}</td>
      <td>${formatDate(project.createdAt)}</td>
      <td>
        <button class="btn btn-sm btn-danger remove-project-btn" data-team-id="${teamId}" data-project-id="${project.id}">移除</button>
      </td>
    `;
    projectsList.appendChild(row);
  });

  hideLoadingState('#team-projects-container');

  // 绑定移除项目按钮事件
  document.querySelectorAll('.remove-project-btn').forEach((btn) => {
    btn.addEventListener('click', function () {
      const projectId = this.getAttribute('data-project-id');
      const teamId = this.getAttribute('data-team-id');
      removeProjectFromTeam(teamId, projectId);
    });
  });
}

// 显示添加项目到团队模态框
function showAddProjectToTeamModal(teamId) {
  document.getElementById('add-project-team-id').value = teamId;
  const projectSelect = document.getElementById('project-select');
  projectSelect.innerHTML = '<option value="">请选择项目</option>';

  // 加载可选项目
  fetch('/api/projects', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getAuthToken()}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error('获取项目列表失败');
      }
      return response.json();
    })
    .then((projects) => {
      // 加载团队已有项目，用于排除
      return fetch(`/api/teams/${teamId}/projects`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getAuthToken()}`,
        },
      }).then((res) => res.json()).then((teamProjects) => {
        const teamProjectIds = teamProjects.map(p => p.id);
        // 过滤掉已在团队中的项目
        const availableProjects = projects.filter(p => !teamProjectIds.includes(p.id));

        availableProjects.forEach((project) => {
          const option = document.createElement('option');
          option.value = project.id;
          option.textContent = project.name;
          projectSelect.appendChild(option);
        });
      });
    })
    .catch((error) => {
      console.error('加载项目列表失败:', error);
      showErrorToast('加载项目列表失败: ' + error.message);
    });

  const modalElement = document.getElementById('addProjectToTeamModal');
  let addProjectToTeamModal = bootstrap.Modal.getInstance(modalElement);
  if (!addProjectToTeamModal) {
    addProjectToTeamModal = new bootstrap.Modal(modalElement);
  }
  addProjectToTeamModal.show();
}

// 添加项目到团队
function addProjectToTeam() {
  const teamId = document.getElementById('add-project-team-id').value;
  const projectId = document.getElementById('project-select').value;

  if (!projectId) {
    showErrorToast('请选择项目');
    return;
  }

  fetch(`/api/teams/${teamId}/projects`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getAuthToken()}`,
    },
    body: JSON.stringify({ projectId }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error('添加项目到团队失败');
      }
      return response.json();
    })
    .then((data) => {
      showSuccessToast('项目添加成功');
      const addProjectToTeamModal = bootstrap.Modal.getInstance(document.getElementById('addProjectToTeamModal'));
      addProjectToTeamModal.hide();
      // 刷新团队项目列表
      loadTeamProjects(teamId);
    })
    .catch((error) => {
      console.error('添加项目到团队失败:', error);
      showErrorToast('添加项目到团队失败: ' + error.message);
    });
}

// 从团队移除项目
function removeProjectFromTeam(teamId, projectId) {
  if (confirm('确定要从团队中移除这个项目吗？')) {
    fetch(`/api/teams/${teamId}/projects/${projectId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getAuthToken()}`,
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error('移除项目失败');
        }
        return response.json();
      })
      .then((data) => {
        showSuccessToast('项目移除成功');
        // 刷新团队项目列表
        loadTeamProjects(teamId);
      })
      .catch((error) => {
        console.error('移除项目失败:', error);
        showErrorToast('移除项目失败: ' + error.message);
      });
  }
}

// 删除团队
function deleteTeam(teamId) {
  if (confirm("确定要删除这个团队吗？")) {
    fetch(`/api/teams/${teamId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getAuthToken()}`,
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("删除团队失败");
        }
        return response.json();
      })
      .then((data) => {
        showSuccessToast("团队删除成功");
        refreshTeamData();
      })
      .catch((error) => {
        console.error("删除团队失败:", error);
        showErrorToast("删除团队失败，请重试");
      });
  }
}

// 显示加载状态
function showLoadingState(selector) {
  const container = document.querySelector(selector);
  container.classList.add("loading");
  container.innerHTML =
    '<div class="text-center py-5"><div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div></div>';
}

// 隐藏加载状态
function hideLoadingState(selector) {
  const container = document.querySelector(selector);
  container.classList.remove("loading");
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString();
}

// 显示成功提示
function showSuccessToast(message) {
  const toastEl = document.getElementById("successToast");
  const toastBody = toastEl.querySelector(".toast-body");
  toastBody.textContent = message;
  const toast = new bootstrap.Toast(toastEl);
  toast.show();
}

// 显示错误提示
function showErrorToast(message) {
  const toastEl = document.getElementById("errorToast");
  const toastBody = toastEl.querySelector(".toast-body");
  toastBody.textContent = message;
  const toast = new bootstrap.Toast(toastEl);
  toast.show();
}

// 获取认证token
function getAuthToken() {
  return localStorage.getItem("token");
}

// 重定向到登录页
function redirectToLogin() {
  window.location.href = "/login";
}

// 绑定保存编辑团队按钮事件
document
  .getElementById("save-edited-team-btn")
  .addEventListener("click", function () {
    saveEditedTeam();
  });

// 绑定添加项目到团队确认按钮事件
document
  .getElementById("confirm-add-project-btn")
  .addEventListener("click", function () {
    addProjectToTeam();
  });

// 为团队项目添加功能，我们需要修改项目创建和编辑页面
// 这里添加一个提示函数，告知用户如何为团队添加项目
function showAddProjectToTeamHint() {
  showInfoToast("您可以在创建或编辑项目时，选择所属团队来将项目添加到团队中。");
}

// 显示信息提示
function showInfoToast(message) {
  // 检查是否有提示容器，如果没有则创建
  let toastContainer = document.querySelector(".toast-container.position-fixed.bottom-0.end-0.p-3");
  if (!toastContainer) {
    toastContainer = document.createElement("div");
    toastContainer.className = "toast-container position-fixed bottom-0 end-0 p-3";
    document.body.appendChild(toastContainer);
  }

  // 检查是否有信息提示框，如果没有则创建
  let infoToast = document.getElementById("infoToast");
  if (!infoToast) {
    const toastHTML = `
      <div id="infoToast" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body">
            <i class="bi bi-info-circle me-2"></i><span id="infoToastMessage"></span>
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;
    toastContainer.innerHTML = toastHTML;
    infoToast = document.getElementById("infoToast");
  }

  document.getElementById("infoToastMessage").textContent = message;
  const toast = new bootstrap.Toast(infoToast);
  toast.show();
}

// 在页面加载完成后，为团队管理页面添加项目提示按钮
document.addEventListener("DOMContentLoaded", function() {
  // 检查是否在团队管理页面
  if (window.location.pathname === '/team-management') {
    // 添加一个提示按钮
    const btnToolbar = document.querySelector(".btn-toolbar");
    if (btnToolbar) {
      const hintBtn = document.createElement("button");
      hintBtn.className = "btn btn-outline-info ms-2";
      hintBtn.innerHTML = '<i class="bi bi-question-circle me-1"></i> 如何添加项目';
      hintBtn.addEventListener("click", showAddProjectToTeamHint);
      btnToolbar.appendChild(hintBtn);
    }
  }
});
