// 用户管理JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // 检查用户是否已登录
  if (!AuthUtils.isLoggedIn()) {
    // 未登录，重定向到登录页面
    window.location.href = '/login';
    return;
  }
  
  // 检查用户是否是管理员
  if (!AuthUtils.isAdmin()) {
    window.location.href = '/';
    return;
  }
  
  // 初始化模态框
  const createAdminModal = new bootstrap.Modal(document.getElementById('createAdminModal'));
  const userDetailModal = new bootstrap.Modal(document.getElementById('userDetailModal'));
  
  // 当前登录用户信息（从localStorage获取）
  const currentUser = AuthUtils.getCurrentUser();

  // 页面加载时获取数据
  loadUserStats();
  loadPendingUsers();
  loadAllUsers();

  // 刷新按钮事件
  document.getElementById('refresh-btn').addEventListener('click', () => {
    loadUserStats();
    loadPendingUsers();
    loadAllUsers();
  });

  // 创建管理员按钮事件
  document.getElementById('create-admin-btn').addEventListener('click', () => {
    document.getElementById('create-admin-form').reset();
    createAdminModal.show();
  });

  // 保存管理员按钮事件
  document.getElementById('save-admin-btn').addEventListener('click', createAdmin);

  // 标签页切换事件
  document.getElementById('all-tab').addEventListener('click', () => {
    loadAllUsers();
  });
  
  document.getElementById('pending-tab').addEventListener('click', () => {
    loadPendingUsers();
  });
  
  // 监听标签页显示事件，确保动画效果
  const allTab = document.getElementById('all');
  const pendingTab = document.getElementById('pending');
  
  allTab.addEventListener('shown.bs.tab', function (e) {
    allTab.classList.add('animate__animated', 'animate__fadeIn');
  });
  
  pendingTab.addEventListener('shown.bs.tab', function (e) {
    pendingTab.classList.add('animate__animated', 'animate__fadeIn');
  });

  /**
   * 加载用户统计信息
   */
  function loadUserStats() {
    AuthUtils.fetchWithAuth('/api/users/admin/stats')
      .then(response => response.json())
      .then(stats => {
        displayUserStats(stats);
      })
      .catch(error => {
        console.error('加载用户统计失败:', error);
        showAlert('加载用户统计失败', 'danger');
      });
  }

  /**
   * 显示用户统计信息
   */
  function displayUserStats(stats) {
    const container = document.getElementById('user-stats');
    container.innerHTML = `
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-people-fill mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.total}</h3>
          <p class="mb-0 text-white-50">总用户数</p>
        </div>
      </div>
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-person-check-fill mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.active}</h3>
          <p class="mb-0 text-white-50">活跃用户</p>
        </div>
      </div>
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-hourglass-split mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.pending}</h3>
          <p class="mb-0 text-white-50">待审核</p>
        </div>
      </div>
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-person-x-fill mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.disabled}</h3>
          <p class="mb-0 text-white-50">已禁用</p>
        </div>
      </div>
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-shield-fill-check mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.admins}</h3>
          <p class="mb-0 text-white-50">管理员</p>
        </div>
      </div>
      <div class="col-md-4 col-lg-2">
        <div class="stats-item text-center p-3">
          <i class="bi bi-person-fill mb-2" style="font-size: 1.75rem;"></i>
          <h3 class="mb-1">${stats.users}</h3>
          <p class="mb-0 text-white-50">普通用户</p>
        </div>
      </div>
    `;
    
    // 更新待审核用户数量徽章
    const pendingCount = document.getElementById('pending-count');
    pendingCount.textContent = stats.pending;
    
    // 如果有待审核用户，添加动画效果
    if (stats.pending > 0) {
      pendingCount.classList.add('animate__animated', 'animate__heartBeat', 'animate__infinite');
    } else {
      pendingCount.classList.remove('animate__animated', 'animate__heartBeat', 'animate__infinite');
    }
  }

  /**
   * 加载待审核用户
   */
  function loadPendingUsers() {
    AuthUtils.fetchWithAuth('/api/users/admin/pending')
      .then(response => response.json())
      .then(users => {
        displayPendingUsers(users);
      })
      .catch(error => {
        console.error('加载待审核用户失败:', error);
        showAlert('加载待审核用户失败', 'danger');
      });
  }

  /**
   * 显示待审核用户
   */
  function displayPendingUsers(users) {
    const container = document.getElementById('pending-users-container');
    
    if (users.length === 0) {
      container.innerHTML = `
        <div class="text-center py-5 animate__animated animate__fadeIn">
          <div class="empty-state mb-3">
            <i class="bi bi-check-circle-fill" style="font-size: 4rem; color: var(--success-color);"></i>
          </div>
          <h4 class="mt-3">暂无待审核用户</h4>
          <p class="text-muted">所有用户都已审核完成</p>
        </div>
      `;
      return;
    }

    let html = '';
    users.forEach((user, index) => {
      // 添加延迟动画效果
      const animationDelay = index * 0.1;
      
      html += `
        <div class="user-card pending-user-card animate__animated animate__fadeInUp" data-id="${user.id}" style="animation-delay: ${animationDelay}s;">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <div class="d-flex align-items-center mb-3">
                <div class="user-avatar me-3 bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                  <i class="bi bi-person" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                  <h5 class="mb-0">${user.displayName || user.username}</h5>
                  <div class="d-flex align-items-center">
                    <small class="text-muted">@${user.username}</small>
                    <span class="badge bg-warning status-badge ms-2 animate__animated animate__pulse animate__infinite animate__slow">待审核</span>
                  </div>
                </div>
              </div>
              <div class="user-info mb-3">
                ${user.email ? `
                  <div class="d-flex align-items-center mb-2">
                    <div class="icon-wrapper me-2 text-primary">
                      <i class="bi bi-envelope"></i>
                    </div>
                    <span>${user.email}</span>
                  </div>
                ` : ''}
                <div class="d-flex align-items-center">
                  <div class="icon-wrapper me-2 text-primary">
                    <i class="bi bi-calendar-event"></i>
                  </div>
                  <span>注册于 ${new Date(user.createdAt).toLocaleString()}</span>
                </div>
              </div>
            </div>
            <div class="action-buttons">
              <button class="btn btn-outline-primary mb-2 w-100" onclick="viewUserDetail('${user.id}')">
                <i class="bi bi-eye me-1"></i> 查看详情
              </button>
              <div class="btn-group w-100" role="group">
                <button class="btn btn-success approve-btn" onclick="approveUser('${user.id}', true)">
                  <i class="bi bi-check-lg me-1"></i> 通过
                </button>
                <button class="btn btn-danger reject-btn" onclick="approveUser('${user.id}', false)">
                  <i class="bi bi-x-lg me-1"></i> 拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;
  }

  /**
   * 加载所有用户
   */
  function loadAllUsers() {
    AuthUtils.fetchWithAuth('/api/users')
      .then(response => response.json())
      .then(users => {
        displayAllUsers(users);
      })
      .catch(error => {
        console.error('加载用户列表失败:', error);
        showAlert('加载用户列表失败', 'danger');
      });
  }

  /**
   * 显示所有用户
   */
  function displayAllUsers(users) {
    const container = document.getElementById('all-users-container');
    
    if (users.length === 0) {
      container.innerHTML = `
        <div class="text-center py-5 animate__animated animate__fadeIn">
          <div class="empty-state mb-3">
            <i class="bi bi-people-fill" style="font-size: 4rem; color: var(--primary-color);"></i>
          </div>
          <h4 class="mt-3">暂无用户</h4>
          <p class="text-muted">系统中还没有任何用户</p>
        </div>
      `;
      return;
    }

    let html = '';
    users.forEach((user, index) => {
      const statusColor = getStatusColor(user.status);
      const roleIcon = user.role === 'admin' ? 'shield-fill-check' : 'person-fill';
      const statusText = getStatusText(user.status);
      // 添加延迟动画效果
      const animationDelay = index * 0.05;
      
      // 根据状态设置头像背景色
      let avatarBgColor = 'bg-secondary';
      if (user.status === 'active') avatarBgColor = 'bg-success';
      if (user.status === 'pending') avatarBgColor = 'bg-warning';
      if (user.status === 'disabled') avatarBgColor = 'bg-danger';
      if (user.role === 'admin') avatarBgColor = 'bg-primary';
      
      html += `
        <div class="user-card animate__animated animate__fadeInUp" style="animation-delay: ${animationDelay}s;">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <div class="d-flex align-items-center mb-3">
                <div class="user-avatar me-3 ${avatarBgColor} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                  <i class="bi bi-${roleIcon}" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                  <h5 class="mb-0">${user.displayName || user.username}</h5>
                  <div class="d-flex align-items-center flex-wrap">
                    <small class="text-muted me-2">@${user.username}</small>
                    <span class="badge bg-${statusColor} status-badge me-1">${statusText}</span>
                    ${user.role === 'admin' ? '<span class="badge bg-primary status-badge">管理员</span>' : ''}
                  </div>
                </div>
              </div>
              
              <div class="user-info mb-3">
                ${user.email ? `
                  <div class="d-flex align-items-center mb-2">
                    <div class="icon-wrapper me-2 text-primary">
                      <i class="bi bi-envelope"></i>
                    </div>
                    <span>${user.email}</span>
                  </div>
                ` : ''}
                
                <div class="row g-2">
                  <div class="col-md-6">
                    <div class="d-flex align-items-center">
                      <div class="icon-wrapper me-2 text-primary">
                        <i class="bi bi-calendar-event"></i>
                      </div>
                      <span>注册于 ${new Date(user.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                  ${user.lastLoginAt ? `
                    <div class="col-md-6">
                      <div class="d-flex align-items-center">
                        <div class="icon-wrapper me-2 text-primary">
                          <i class="bi bi-clock-history"></i>
                        </div>
                        <span>最后登录 ${new Date(user.lastLoginAt).toLocaleString()}</span>
                      </div>
                    </div>
                  ` : ''}
                </div>
              </div>
            </div>
            
            <div class="action-buttons">
              <button class="btn btn-outline-primary mb-2 w-100" onclick="viewUserDetail('${user.id}')">
                <i class="bi bi-eye me-1"></i> 查看详情
              </button>
              <div class="d-flex gap-2">
                ${user.status === 'active' ? `
                  <button class="btn btn-warning flex-grow-1" onclick="updateUserStatus('${user.id}', 'disabled')">
                    <i class="bi bi-slash-circle me-1"></i> 禁用
                  </button>
                ` : user.status === 'disabled' ? `
                  <button class="btn btn-success flex-grow-1" onclick="updateUserStatus('${user.id}', 'active')">
                    <i class="bi bi-check-circle me-1"></i> 启用
                  </button>
                ` : ''}
                ${user.id !== currentUser.id ? `
                  <button class="btn btn-outline-danger flex-grow-1" onclick="deleteUser('${user.id}', '${user.username}')">
                    <i class="bi bi-trash me-1"></i> 删除
                  </button>
                ` : ''}
              </div>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;
  }

  /**
   * 创建管理员
   */
  function createAdmin() {
    const username = document.getElementById('admin-username').value;
    const password = document.getElementById('admin-password').value;
    const email = document.getElementById('admin-email').value;
    const displayName = document.getElementById('admin-displayName').value;

    if (!username || !password) {
      showAlert('用户名和密码为必填项', 'danger');
      return;
    }

    AuthUtils.fetchWithAuth('/api/users/admin/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username,
        password,
        email,
        displayName
      })
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          showAlert(data.error, 'danger');
          return;
        }
        
        createAdminModal.hide();
        loadUserStats();
        loadAllUsers();
        showAlert('管理员创建成功', 'success');
      })
      .catch(error => {
        console.error('创建管理员失败:', error);
        showAlert('创建管理员失败', 'danger');
      });
  }

  /**
   * 审核用户
   */
  window.approveUser = function(userId, approved) {
    const action = approved ? '通过' : '拒绝';
    if (confirm(`确定要${action}这个用户的注册申请吗？`)) {
      // 找到对应的用户卡片并添加加载状态
      const userCard = document.querySelector(`.pending-user-card[data-id="${userId}"]`);
      if (userCard) {
        userCard.classList.add('processing');
        const approveBtn = userCard.querySelector('.approve-btn');
        const rejectBtn = userCard.querySelector('.reject-btn');
        if (approveBtn) approveBtn.disabled = true;
        if (rejectBtn) rejectBtn.disabled = true;
        
        // 添加加载指示器
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75';
        loadingSpinner.innerHTML = `
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">处理中...</span>
          </div>
        `;
        userCard.style.position = 'relative';
        userCard.appendChild(loadingSpinner);
      }
      
      AuthUtils.fetchWithAuth(`/api/users/${userId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          approved,
          adminId: currentUser.id
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            // 移除加载状态
            if (userCard) {
              userCard.classList.remove('processing');
              const loadingSpinner = userCard.querySelector('.position-absolute');
              if (loadingSpinner) loadingSpinner.remove();
              
              const actionButtons = userCard.querySelectorAll('button');
              actionButtons.forEach(btn => btn.disabled = false);
              
              // 添加错误提示动画
              userCard.classList.add('animate__animated', 'animate__shakeX');
              setTimeout(() => {
                userCard.classList.remove('animate__animated', 'animate__shakeX');
              }, 1000);
            }
            return;
          }
          
          if (userCard) {
            // 添加成功动画
            userCard.classList.add('animate__animated', 'animate__fadeOutRight');
            setTimeout(() => {
              userCard.remove();
              // 检查是否还有待审核用户
              const remainingCards = document.querySelectorAll('.user-card');
              if (remainingCards.length === 0) {
                const container = document.getElementById('pending-users-container');
                container.innerHTML = `
                  <div class="text-center py-5 animate__animated animate__fadeIn">
                    <div class="empty-state mb-3">
                      <i class="bi bi-check-circle-fill" style="font-size: 4rem; color: var(--success-color);"></i>
                    </div>
                    <h4 class="mt-3">暂无待审核用户</h4>
                    <p class="text-muted">所有用户都已审核完成</p>
                  </div>
                `;
              }
            }, 500);
          }
          
          loadUserStats();
          loadPendingUsers();
          loadAllUsers();
          showAlert(data.message, 'success');
        })
        .catch(error => {
          console.error('审核用户失败:', error);
          showAlert('审核用户失败', 'danger');
          
          // 移除加载状态
          if (userCard) {
            userCard.classList.remove('processing');
            const loadingSpinner = userCard.querySelector('.position-absolute');
            if (loadingSpinner) loadingSpinner.remove();
            
            const actionButtons = userCard.querySelectorAll('button');
            actionButtons.forEach(btn => btn.disabled = false);
            
            // 添加错误提示动画
            userCard.classList.add('animate__animated', 'animate__shakeX');
            setTimeout(() => {
              userCard.classList.remove('animate__animated', 'animate__shakeX');
            }, 1000);
          }
        });
    }
  };

  /**
   * 更新用户状态
   */
  window.updateUserStatus = function(userId, status) {
    const statusText = status === 'active' ? '启用' : '禁用';
    if (confirm(`确定要${statusText}这个用户吗？`)) {
      // 找到对应的用户卡片并添加加载状态
      const userCard = document.querySelector(`.user-card[data-id="${userId}"]`);
      if (userCard) {
        userCard.classList.add('processing');
        const actionButtons = userCard.querySelectorAll('button');
        actionButtons.forEach(btn => btn.disabled = true);
        
        // 添加加载指示器
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75';
        loadingSpinner.innerHTML = `
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">处理中...</span>
          </div>
        `;
        userCard.style.position = 'relative';
        userCard.appendChild(loadingSpinner);
      }
      
      AuthUtils.fetchWithAuth(`/api/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          adminId: currentUser.id
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            // 移除加载状态
            if (userCard) {
              userCard.classList.remove('processing');
              const loadingSpinner = userCard.querySelector('.position-absolute');
              if (loadingSpinner) loadingSpinner.remove();
              
              const actionButtons = userCard.querySelectorAll('button');
              actionButtons.forEach(btn => btn.disabled = false);
              
              // 添加错误提示动画
              userCard.classList.add('animate__animated', 'animate__shakeX');
              setTimeout(() => {
                userCard.classList.remove('animate__animated', 'animate__shakeX');
              }, 1000);
            }
            return;
          }
          
          // 移除加载状态并添加成功动画
          if (userCard) {
            const loadingSpinner = userCard.querySelector('.position-absolute');
            if (loadingSpinner) loadingSpinner.remove();
            
            userCard.classList.remove('processing');
            userCard.classList.add('animate__animated', 'animate__pulse');
            
            setTimeout(() => {
              userCard.classList.remove('animate__animated', 'animate__pulse');
              loadUserStats();
              loadAllUsers();
            }, 800);
          } else {
            loadUserStats();
            loadAllUsers();
          }
          
          showAlert(data.message, 'success');
        })
        .catch(error => {
          console.error('更新用户状态失败:', error);
          showAlert('更新用户状态失败', 'danger');
          
          // 移除加载状态
          if (userCard) {
            userCard.classList.remove('processing');
            const loadingSpinner = userCard.querySelector('.position-absolute');
            if (loadingSpinner) loadingSpinner.remove();
            
            const actionButtons = userCard.querySelectorAll('button');
            actionButtons.forEach(btn => btn.disabled = false);
            
            // 添加错误提示动画
            userCard.classList.add('animate__animated', 'animate__shakeX');
            setTimeout(() => {
              userCard.classList.remove('animate__animated', 'animate__shakeX');
            }, 1000);
          }
        });
    }
  };

  /**
   * 删除用户
   */
  window.deleteUser = function(userId, username) {
    // 创建一个更美观的确认对话框
    const confirmDialog = document.createElement('div');
    confirmDialog.className = 'modal fade';
    confirmDialog.id = 'deleteConfirmModal';
    confirmDialog.setAttribute('tabindex', '-1');
    confirmDialog.setAttribute('aria-labelledby', 'deleteConfirmModalLabel');
    confirmDialog.setAttribute('aria-hidden', 'true');
    
    confirmDialog.innerHTML = `
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="deleteConfirmModalLabel">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>删除用户确认
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="text-center mb-4">
              <div class="avatar-placeholder bg-danger text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                <i class="bi bi-person-x-fill" style="font-size: 2.5rem;"></i>
              </div>
            </div>
            <p class="text-center fs-5 mb-1">您确定要删除以下用户吗？</p>
            <p class="text-center fw-bold fs-4 mb-3">${username}</p>
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-circle-fill me-2"></i>
              <strong>警告：</strong> 此操作将永久删除该用户的所有数据，且不可恢复！
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-circle me-1"></i>取消
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
              <i class="bi bi-trash me-1"></i>确认删除
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(confirmDialog);
    
    // 显示确认对话框
    const modal = new bootstrap.Modal(confirmDialog);
    modal.show();
    
    // 确认删除按钮点击事件
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
      // 禁用按钮并显示加载状态
      this.disabled = true;
      this.innerHTML = `<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>正在删除...`;
      
      // 找到对应的用户卡片
      const userCard = document.querySelector(`.user-card[data-id="${userId}"]`);
      
      AuthUtils.fetchWithAuth(`/api/users/${userId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          // 隐藏确认对话框
          modal.hide();
          
          // 从DOM中移除确认对话框
          confirmDialog.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(confirmDialog);
          });
          
          // 如果找到用户卡片，添加动画效果
          if (userCard) {
            userCard.classList.add('animate__animated', 'animate__fadeOutLeft');
            setTimeout(() => {
              loadUserStats();
              loadPendingUsers();
              loadAllUsers();
            }, 500);
          } else {
            loadUserStats();
            loadPendingUsers();
            loadAllUsers();
          }
          
          showAlert('用户删除成功', 'success');
        })
        .catch(error => {
          console.error('删除用户失败:', error);
          
          // 恢复按钮状态
          this.disabled = false;
          this.innerHTML = `<i class="bi bi-trash me-1"></i>确认删除`;
          
          // 显示错误消息
          const errorAlert = document.createElement('div');
          errorAlert.className = 'alert alert-danger mt-3 mb-0';
          errorAlert.innerHTML = `<i class="bi bi-exclamation-circle-fill me-2"></i>删除用户失败`;
          
          const modalBody = confirmDialog.querySelector('.modal-body');
          modalBody.appendChild(errorAlert);
          
          // 5秒后自动移除错误提示
          setTimeout(() => {
            if (modalBody.contains(errorAlert)) {
              modalBody.removeChild(errorAlert);
            }
          }, 5000);
        });
    });
    
    // 对话框关闭时移除元素
    confirmDialog.addEventListener('hidden.bs.modal', function() {
      if (document.body.contains(confirmDialog)) {
        document.body.removeChild(confirmDialog);
      }
    });
  };


  /**
   * 查看用户详情
   */
  window.viewUserDetail = function(userId) {
    AuthUtils.fetchWithAuth(`/api/users/${userId}`)
      .then(response => response.json())
      .then(user => {
        if (user.error) {
          showAlert(user.error, 'danger');
          return;
        }
        
        displayUserDetail(user);
        userDetailModal.show();
      })
      .catch(error => {
        console.error('获取用户详情失败:', error);
        showAlert('获取用户详情失败', 'danger');
      });
  };

  /**
   * 显示用户详情
   */
  function displayUserDetail(user) {
    const content = document.getElementById('user-detail-content');
    const statusColor = getStatusColor(user.status);
    const roleIcon = user.role === 'admin' ? 'shield-fill-check' : 'person-fill';
    
    // 根据状态设置头像背景色
    let avatarBgColor = 'bg-secondary';
    if (user.status === 'active') avatarBgColor = 'bg-success';
    if (user.status === 'pending') avatarBgColor = 'bg-warning';
    if (user.status === 'disabled') avatarBgColor = 'bg-danger';
    if (user.role === 'admin') avatarBgColor = 'bg-primary';
    
    content.innerHTML = `
      <div class="user-detail-header animate__animated animate__fadeIn">
        <div class="d-flex align-items-center mb-4">
          <div class="user-avatar me-3 ${avatarBgColor} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 64px; height: 64px;">
            <i class="bi bi-${roleIcon}" style="font-size: 2rem;"></i>
          </div>
          <div>
            <h4 class="mb-0">${user.displayName || user.username}</h4>
            <div class="d-flex align-items-center flex-wrap mt-1">
              <small class="text-muted me-2">@${user.username}</small>
              <span class="badge bg-${statusColor} me-1">${getStatusText(user.status)}</span>
              ${user.role === 'admin' ? '<span class="badge bg-primary">管理员</span>' : ''}
            </div>
          </div>
        </div>
      </div>

      <div class="row animate__animated animate__fadeIn" style="animation-delay: 0.1s;">
        <div class="col-md-6">
          <div class="section-header d-flex align-items-center mb-3">
            <div class="section-icon me-2 text-primary">
              <i class="bi bi-info-circle-fill"></i>
            </div>
            <h5 class="mb-0">基本信息</h5>
          </div>
          <div class="detail-card p-3 rounded shadow-sm">
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">用户名</div>
              <div class="detail-value">${user.username}</div>
            </div>
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">显示名称</div>
              <div class="detail-value">${user.displayName || '-'}</div>
            </div>
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">邮箱</div>
              <div class="detail-value">
                ${user.email ? `<i class="bi bi-envelope text-primary me-1"></i>${user.email}` : '-'}
              </div>
            </div>
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">角色</div>
              <div class="detail-value">
                <span class="badge ${user.role === 'admin' ? 'bg-primary' : 'bg-secondary'}">
                  ${user.role === 'admin' ? '管理员' : '普通用户'}
                </span>
              </div>
            </div>
            <div class="detail-item d-flex py-2">
              <div class="detail-label fw-bold" style="width: 120px;">状态</div>
              <div class="detail-value">
                <span class="badge bg-${statusColor}">${getStatusText(user.status)}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="section-header d-flex align-items-center mb-3">
            <div class="section-icon me-2 text-primary">
              <i class="bi bi-clock-history"></i>
            </div>
            <h5 class="mb-0">时间信息</h5>
          </div>
          <div class="detail-card p-3 rounded shadow-sm">
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">注册时间</div>
              <div class="detail-value">
                <i class="bi bi-calendar-event text-primary me-1"></i>
                ${new Date(user.createdAt).toLocaleString()}
              </div>
            </div>
            <div class="detail-item d-flex py-2 border-bottom">
              <div class="detail-label fw-bold" style="width: 120px;">更新时间</div>
              <div class="detail-value">
                <i class="bi bi-arrow-clockwise text-primary me-1"></i>
                ${new Date(user.updatedAt).toLocaleString()}
              </div>
            </div>
            ${user.approvedAt ? `
              <div class="detail-item d-flex py-2 border-bottom">
                <div class="detail-label fw-bold" style="width: 120px;">审核时间</div>
                <div class="detail-value">
                  <i class="bi bi-check-circle text-primary me-1"></i>
                  ${new Date(user.approvedAt).toLocaleString()}
                </div>
              </div>
            ` : ''}
            ${user.lastLoginAt ? `
              <div class="detail-item d-flex py-2">
                <div class="detail-label fw-bold" style="width: 120px;">最后登录</div>
                <div class="detail-value">
                  <i class="bi bi-box-arrow-in-right text-primary me-1"></i>
                  ${new Date(user.lastLoginAt).toLocaleString()}
                </div>
              </div>
            ` : ''}
          </div>
        </div>
      </div>
      
      <div class="text-center text-muted mt-4 animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
        <small><i class="bi bi-info-circle me-1"></i> 用户信息最后更新于 ${new Date().toLocaleString()}</small>
      </div>
    `;
  }

  /**
   * 获取状态颜色
   */
  function getStatusColor(status) {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'disabled': return 'danger';
      default: return 'secondary';
    }
  }

  /**
   * 获取状态文本
   */
  function getStatusText(status) {
    switch (status) {
      case 'active': return '活跃';
      case 'pending': return '待审核';
      case 'disabled': return '已禁用';
      default: return '未知';
    }
  }

  /**
   * 显示提示信息
   * @param {string} message - 提示内容
   * @param {string} type - 提示类型：success, danger, warning, info
   */
  function showAlert(message, type = 'info') {
    // 创建提示容器（如果不存在）
    let alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
      alertContainer = document.createElement('div');
      alertContainer.id = 'alert-container';
      alertContainer.className = 'position-fixed top-20 end-3';
      alertContainer.style.cssText = 'z-index: 9999; max-width: 400px; right: 20px; top: 20px;';
      document.body.appendChild(alertContainer);
    }
    
    // 根据类型设置图标
    let icon = 'info-circle-fill';
    if (type === 'success') icon = 'check-circle-fill';
    if (type === 'danger') icon = 'exclamation-triangle-fill';
    if (type === 'warning') icon = 'exclamation-circle-fill';
    
    // 创建提示元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show animate__animated animate__fadeInRight shadow-sm`;
    alertDiv.innerHTML = `
      <div class="d-flex align-items-center">
        <div class="alert-icon me-3">
          <i class="bi bi-${icon}" style="font-size: 1.5rem;"></i>
        </div>
        <div class="alert-content">
          ${message}
        </div>
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 添加到容器
    alertContainer.appendChild(alertDiv);
    
    // 添加自动消失的动画效果
    setTimeout(() => {
      alertDiv.classList.remove('animate__fadeInRight');
      alertDiv.classList.add('animate__fadeOutRight');
      
      // 动画结束后移除元素
      alertDiv.addEventListener('animationend', () => {
        if (alertContainer.contains(alertDiv)) {
          alertContainer.removeChild(alertDiv);
        }
      });
    }, 4000);
    
    // 点击关闭按钮时添加动画
    const closeButton = alertDiv.querySelector('.btn-close');
    closeButton.addEventListener('click', (e) => {
      e.preventDefault();
      alertDiv.classList.remove('animate__fadeInRight');
      alertDiv.classList.add('animate__fadeOutRight');
    });
  }
});
