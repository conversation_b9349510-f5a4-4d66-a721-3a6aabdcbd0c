const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const ProjectServer = require('./ProjectServer');
const Team = require('./Team'); // 导入团队模型

class Project {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'configs');
    fs.ensureDirSync(this.configDir);
  }

  // 获取所有项目
  async getAll() {
    try {
      const files = await fs.readdir(this.configDir);
      const projects = [];
      
      for (const file of files) {
        if (file.endsWith('.json') && !['servers.json', 'project_servers.json','vcs_credentials.json','teams.json','deployment_history.json'].includes(file)) {

          const projectData = await fs.readJson(path.join(this.configDir, file));
          
          // 获取项目的服务器配置
          const serverConfigs = await ProjectServer.getByProjectId(projectData.id);
          
          // 添加servers字段
          projects.push({
            ...projectData,
            servers: serverConfigs
          });
        }
      }
      
      return projects;
    } catch (error) {
      throw new Error(`获取项目列表失败: ${error.message}`);
    }
  }

  // 根据ID获取项目
  async getById(id) {
    try {
      const filePath = path.join(this.configDir, `${id}.json`);
      if (await fs.pathExists(filePath)) {
        const projectData = await fs.readJson(filePath);
        
        // 获取项目的服务器配置
        const serverConfigs = await ProjectServer.getByProjectId(id);
        
        // 返回包含servers字段的项目对象
        return {
          ...projectData,
          servers: serverConfigs
        };
      }
      return null;
    } catch (error) {
      throw new Error(`获取项目详情失败: ${error.message}`);
    }
  }

  // 创建新项目
  async create(projectData) {
    try {
      const id = uuidv4();
      const project = {
        id,
        name: projectData.name,
        gitUrl: projectData.gitUrl, // 兼容旧字段名
        vcsType: projectData.vcsType || 'git', // 版本控制系统类型：git 或 svn
        vcsCredentialId: projectData.vcsCredentialId || '', // VCS凭据ID
        description: projectData.description || '',
        buildCommand: projectData.buildCommand || 'npm run build',
        outputDir: projectData.outputDir || 'dist',
        branches: projectData.branches || ['main'],
        nodeVersion: projectData.nodeVersion || '',
        packageManager: projectData.packageManager || 'npm',

        teamId: projectData.teamId || '', // 项目所属团队ID
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      await fs.writeJson(path.join(this.configDir, `${id}.json`), project, { spaces: 2 });
      
      // 保存项目与服务器的关系，如果提供了servers参数
      if (projectData.servers && Array.isArray(projectData.servers)) {
        const serverConfigs = projectData.servers.map(config => {
          // 检查是对象（新格式）还是字符串ID（旧格式）
          if (typeof config === 'object' && config.serverId) {
            return {
              serverId: config.serverId,
              deployPath: config.deployPath || '/var/www/html'
            };
          } else if (typeof config === 'string') {
            // 旧格式，使用默认部署路径
            return {
              serverId: config,
              deployPath: '/var/www/html'
            };
          }
          return null;
        }).filter(Boolean);
        
        await ProjectServer.setProjectServers(id, serverConfigs);
      }
      
      return project;
    } catch (error) {
      throw new Error(`创建项目失败: ${error.message}`);
    }
  }

  // 更新项目
  async update(id, projectData) {
    try {
      const filePath = path.join(this.configDir, `${id}.json`);
      if (!(await fs.pathExists(filePath))) {
        throw new Error('项目不存在');
      }
      
      const existingProject = await fs.readJson(filePath);
      
      // 从projectData中提取servers数据，不保存到项目JSON中
      const { servers, ...projectDataWithoutServers } = projectData;
      
      // 确保保留teamId（如果projectData中没有提供，则使用现有值）
      const teamId = projectData.teamId !== undefined ? projectData.teamId : existingProject.teamId;
      
      const updatedProject = {
        ...existingProject,
        ...projectDataWithoutServers,
        teamId,
        updatedAt: new Date().toISOString()
      };

      
      await fs.writeJson(filePath, updatedProject, { spaces: 2 });
      
      // 更新项目与服务器的关系，如果提供了servers参数
      if (servers && Array.isArray(servers)) {
        const serverConfigs = servers.map(config => {
          // 检查是对象（新格式）还是字符串ID（旧格式）
          if (typeof config === 'object' && config.serverId) {
            return {
              serverId: config.serverId,
              deployPath: config.deployPath || '/var/www/html'
            };
          } else if (typeof config === 'string') {
            // 旧格式，使用默认部署路径
            return {
              serverId: config,
              deployPath: '/var/www/html'
            };
          }
          return null;
        }).filter(Boolean);
        
        await ProjectServer.setProjectServers(id, serverConfigs);
      }
      
      return updatedProject;
    } catch (error) {
      throw new Error(`更新项目失败: ${error.message}`);
    }
  }

  // 删除项目
  async delete(id) {
    try {
      const filePath = path.join(this.configDir, `${id}.json`);
      if (await fs.pathExists(filePath)) {
        // 删除项目文件
        await fs.remove(filePath);
        
        // 删除项目与服务器的关系
        await ProjectServer.removeAllProjectRelationships(id);
        
        return true;
      }
      return false;
    } catch (error) {
      throw new Error(`删除项目失败: ${error.message}`);
    }
  }
  
  // 获取项目的服务器配置（包含部署路径）
  async getProjectServerConfigs(id) {
    try {
      // 获取项目与服务器的关系
      const relationships = await ProjectServer.getByProjectId(id);
      return relationships;
    } catch (error) {
      throw new Error(`获取项目服务器配置失败: ${error.message}`);
    }
  }

  // 根据团队ID获取项目
  async getByTeamId(teamId) {
    try {
      const files = await fs.readdir(this.configDir);
      const projects = [];

      for (const file of files) {
        if (file.endsWith('.json') && !['servers.json', 'project_servers.json','vcs_credentials.json'].includes(file)) {
          const projectData = await fs.readJson(path.join(this.configDir, file));

          // 只添加指定团队的项目
          if (projectData.teamId === teamId) {
            // 获取项目的服务器配置
            const serverConfigs = await ProjectServer.getByProjectId(projectData.id);

            // 添加servers字段
            projects.push({
              ...projectData,
              servers: serverConfigs
            });
          }
        }
      }

      return projects;
    } catch (error) {
      throw new Error(`获取团队项目列表失败: ${error.message}`);
    }
  }
}

module.exports = new Project();