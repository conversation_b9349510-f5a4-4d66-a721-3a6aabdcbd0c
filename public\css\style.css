/* 全局样式 */
body {
  font-size: .875rem;
}

.feather {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

/* 侧边栏 */
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

@media (max-width: 767.98px) {
  .sidebar {
    top: 5rem;
  }
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
}

.sidebar .nav-link.active {
  color: #0d6efd;
}

.sidebar .nav-link:hover {
  color: #0d6efd;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/* 导航栏 */
.navbar-brand {
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  background-color: rgba(0, 0, 0, .25);
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .navbar-toggler {
  top: .25rem;
  right: 1rem;
}

/* 内容区域 */
.page-content {
  display: none;
}

.page-content.active {
  display: block;
}

/* 日志样式 */
.log-content {
  background-color: #212529;
  color: #f8f9fa;
  font-family: monospace;
  padding: 1rem;
  border-radius: 0.25rem;
  height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.deployment-log-container {
  margin-top: 1rem;
}

/* 表格样式 */
.table-responsive {
  margin-bottom: 1rem;
}

/* 表单样式 */
.form-check {
  margin-bottom: 0.5rem;
}

/* 状态徽章 */
.badge {
  font-weight: 500;
}

/* 卡片样式 */
.card {
  margin-bottom: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* 按钮样式 */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 用户信息 */
.user-info {
  margin-top: 1rem;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 576px) {
  .card-body {
    padding: 0.75rem;
  }
  
  .table td, .table th {
    padding: 0.5rem;
  }
}

/* 提示框样式 */
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

/* 加载动画 */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

/* 构建队列页面样式 */
.build-info {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

/* 登录和注册页面样式 */
.form-signin, .form-register {
  max-width: 400px;
  margin: 0 auto;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
} 