{"name": "frontend-deploy-tool", "version": "1.0.0", "description": "Web-based frontend project deployment tool", "main": "main.js", "scripts": {"start": "node main.js", "dev": "nodemon main.js"}, "dependencies": {"archiver": "^6.0.1", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "extract-zip": "^2.0.1", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-ssh": "^13.1.0", "nodemailer": "^6.9.5", "simple-git": "^3.20.0", "socket.io": "^4.7.2", "uuid": "^9.0.0", "winston": "^3.10.0", "xml2js": "^0.6.2"}, "devDependencies": {"copy-webpack-plugin": "^13.0.0", "node-loader": "^2.1.0", "nodemon": "^3.0.1", "ts-loader": "^9.5.2", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}