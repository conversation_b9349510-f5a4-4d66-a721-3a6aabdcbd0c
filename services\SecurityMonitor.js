const fs = require('fs-extra');
const path = require('path');
const nodemailer = require('nodemailer'); // 需要安装此依赖
const dotenv = require('dotenv');
// 加载环境变量
dotenv.config();
class SecurityMonitor {
  constructor() {
    this.logFile = path.join(process.cwd(), 'logs/security/injection.log');
    this.lastCheckTime = new Date();
    this.alertThreshold = 1; // 3次以上尝试触发警报
    this.checkInterval = 1000 * 60 * 30; // 每10分钟分钟分钟分钟检查一次
    
    // 从环境变量加载邮件配置
    this.emailConfig = {
      enabled: process.env.EMAIL_ENABLED === 'true',
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
      from: process.env.EMAIL_FROM,
      to: process.env.EMAIL_TO
    };
  }
  
  /**
   * 启动安全监控
   */
  start() {
    // 定期检查安全日志
    setInterval(() => this.checkSecurityLogs(), this.checkInterval);
    console.log('安全监控服务已启动');
  }
  
  /**
   * 检查安全日志
   */
  async checkSecurityLogs() {
    try {
      if (!await fs.pathExists(this.logFile)) {
        return;
      }
      
      // 读取日志文件
      const content = await fs.readFile(this.logFile, 'utf8');
      const logs = content.split('\n')
        .filter(line => line.trim())
        .map(line => JSON.parse(line));
      
      // 过滤出上次检查后的新日志
      const newLogs = logs.filter(log => {
        const logTime = new Date(log.timestamp);
        return logTime > this.lastCheckTime;
      });
      
      this.lastCheckTime = new Date();
      
      // 如果没有新日志，直接返回
      if (newLogs.length === 0) {
        return;
      }
      
      // 按IP分组
      const ipGroups = {};
      newLogs.forEach(log => {
        const ip = log.ip || 'unknown';
        if (!ipGroups[ip]) {
          ipGroups[ip] = [];
        }
        ipGroups[ip].push(log);
      });
      
      // 检查是否有IP超过阈值
      for (const ip in ipGroups) {
        if (ipGroups[ip].length >= this.alertThreshold) {
          await this.sendAlert({
            ip,
            count: ipGroups[ip].length,
            logs: ipGroups[ip]
          });
        }
      }
    } catch (error) {
      console.error('检查安全日志失败:', error);
    }
  }
  
  /**
   * 发送警报
   * @param {object} alertData - 警报数据
   */
  async sendAlert(alertData) {
    console.warn('检测到安全威胁!', alertData);
    
    // 如果启用了邮件警报，发送邮件
    if (this.emailConfig.enabled) {
      try {
        const transporter = nodemailer.createTransport({
          host: this.emailConfig.host,
          port: this.emailConfig.port,
          secure: this.emailConfig.secure,
          auth: {
            user: this.emailConfig.user,
            pass: this.emailConfig.pass
          }
        });
        
        await transporter.sendMail({
          from: this.emailConfig.from,
          to: this.emailConfig.to,
          subject: `[安全警报] 检测到可能的命令注入攻击`,
          text: `
            检测到来自IP ${alertData.ip} 的可能命令注入攻击
            尝试次数: ${alertData.count}
            时间: ${new Date().toISOString()}
            
            详细日志:
            ${JSON.stringify(alertData.logs, null, 2)}
          `,
          html: `
            <h2>安全警报</h2>
            <p>检测到来自IP <strong>${alertData.ip}</strong> 的可能命令注入攻击</p>
            <p>尝试次数: <strong>${alertData.count}</strong></p>
            <p>时间: ${new Date().toISOString()}</p>
            
            <h3>详细日志:</h3>
            <pre>${JSON.stringify(alertData.logs, null, 2)}</pre>
          `
        });
        
        console.log('安全警报邮件已发送');
      } catch (error) {
        console.error('发送安全警报邮件失败:', error);
      }
    }
  }
}

module.exports = new SecurityMonitor();