<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端部署工具 - 登录</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/animate.min.css">
  <style>
    :root {
      --primary-color: #4e73df;
      --secondary-color: #6f42c1;
      --success-color: #1cc88a;
      --accent-color: #36b9cc;
    }
    
    body {
      background: linear-gradient(135deg, #f8f9fc 0%, #e8f0fe 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
      font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
      opacity: 0;
      transition: opacity 0.6s ease-in-out;
    }
    
    body.loaded {
      opacity: 1;
    }
    
    .form-signin {
      width: 100%;
      max-width: 450px;
      padding: 15px;
      margin: auto;
      animation: fadeIn 0.8s ease-in-out 0.3s forwards;
      opacity: 0;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.95);
    }
    
    .card:hover {
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 2.5rem;
    }
    
    .form-floating:focus-within {
      z-index: 2;
    }
    
    .form-control {
      border-radius: 8px;
      padding: 12px;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
    }
    
    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    
    .form-floating > label {
      padding: 12px;
    }
    
    .form-signin input[type="text"] {
      margin-bottom: 15px;
    }
    
    .form-signin input[type="password"] {
      margin-bottom: 20px;
    }
    
    .btn-primary {
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      border: none;
      border-radius: 8px;
      padding: 12px;
      font-weight: 600;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .btn-primary:hover {
      background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
    }
    
    .btn-primary:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.6s ease-out, height 0.6s ease-out;
    }
    
    .btn-primary:active:before {
      width: 300px;
      height: 300px;
    }
    
    .login-logo {
      font-size: 3.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    .alert-danger {
      border-radius: 8px;
      border-left: 4px solid #e74a3b;
      background-color: rgba(231, 74, 59, 0.1);
    }
    
    a {
      color: var(--primary-color);
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;
    }
    
    a:hover {
      color: var(--secondary-color);
    }
    
    a:after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -2px;
      left: 0;
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      transition: width 0.3s ease;
    }
    
    a:hover:after {
      width: 100%;
    }
    
    .footer {
      margin-top: 2rem;
      text-align: center;
      font-size: 0.85rem;
      color: #6c757d;
      animation: fadeIn 0.8s ease-in-out 0.6s forwards;
      opacity: 0;
    }
    
    .badge {
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      padding: 0.5rem 1rem;
      border-radius: 30px;
      box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
      transition: all 0.3s ease;
    }
    
    .badge:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(78, 115, 223, 0.4);
    }
    
    /* 添加背景动画效果 */
    @keyframes gradientAnimation {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    
    body:before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(-45deg, rgba(78, 115, 223, 0.05), rgba(111, 66, 193, 0.05), rgba(54, 185, 204, 0.05));
      background-size: 400% 400%;
      animation: gradientAnimation 15s ease infinite;
      z-index: -1;
    }
    
    /* 添加输入框聚焦效果 */
    .form-floating.focused label {
      color: var(--primary-color);
      font-weight: 500;
    }
    
    /* 添加按钮成功状态 */
    .btn-success {
      background: linear-gradient(to right, var(--success-color), #25e2bc);
      border: none;
    }
    
    .btn-success:hover {
      background: linear-gradient(to right, #25e2bc, var(--success-color));
      box-shadow: 0 5px 15px rgba(28, 200, 138, 0.4);
    }
  </style>
</head>
<body>
  <main class="form-signin">
    <div class="text-center mb-4">
      <div class="login-logo">
        <i class="bi bi-cloud-upload"></i>
      </div>
      <h1 class="h2 mb-2 fw-bold">前端部署工具</h1>
      <!-- <div class="badge bg-primary bg-gradient mb-3 px-3 py-2 rounded-pill">
        <i class="bi bi-code-slash me-1"></i> 高效 · 便捷
      </div> -->
      <!-- <p class="text-muted mb-4">请登录您的账户以继续使用</p> -->
    </div>
    
    <div class="card">
      <div class="card-body">
        <h5 class="card-title text-center mb-4">账户登录</h5>
        <form id="login-form">
          <div class="alert alert-danger d-none" id="login-error">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span id="error-message"></span>
          </div>
          
          <div class="form-floating mb-3">
            <input type="text" class="form-control" id="username" placeholder="用户名" required autocomplete="username">
            <label for="username"><i class="bi bi-person me-1"></i> 用户名</label>
          </div>
          
          <div class="form-floating mb-4">
            <input type="password" class="form-control" id="password" placeholder="密码" required autocomplete="current-password">
            <label for="password"><i class="bi bi-lock me-1"></i> 密码</label>
          </div>
          
          <button class="w-100 btn btn-lg btn-primary" type="submit">
            <i class="bi bi-box-arrow-in-right me-2"></i> 登录系统
            <span class="ms-2 spinner-border spinner-border-sm d-none" id="login-spinner" role="status" aria-hidden="true"></span>
          </button>
        </form>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <p>还没有账号？ <a href="/register" class="fw-bold"><i class="bi bi-person-plus"></i> 立即注册</a></p>
    </div>
    
    <div class="footer">
      <p>© 2025 前端部署工具 | 仅供精准教学组内部使用</p>
    </div>
  </main>
  
  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const loginForm = document.getElementById('login-form');
      const loginError = document.getElementById('login-error');
      const errorMessage = document.getElementById('error-message');
      const loginSpinner = document.getElementById('login-spinner');
      const loginButton = loginForm.querySelector('button[type="submit"]');
      const usernameInput = document.getElementById('username');
      const passwordInput = document.getElementById('password');
      
      // 添加输入框动画效果
      [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('focus', () => {
          input.parentElement.classList.add('shadow-sm', 'focused');
        });
        
        input.addEventListener('blur', () => {
          input.parentElement.classList.remove('shadow-sm', 'focused');
        });
      });
      
      // 自动聚焦用户名输入框
      setTimeout(() => {
        usernameInput.focus();
      }, 500);
      
      loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        
        // 简单的前端验证
        if (!username) {
          showError('请输入用户名');
          usernameInput.focus();
          return;
        }
        
        if (!password) {
          showError('请输入密码');
          passwordInput.focus();
          return;
        }
        
        try {
          // 显示加载动画
          showLoading(true);
          loginError.classList.add('d-none');
          
          const response = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            // 根据状态码和错误信息提供更详细的提示
            let message = data.error || '登录失败';

            if (response.status === 403 && data.status) {
              switch (data.status) {
                case 'pending':
                  message = '您的账户正在等待管理员审核，请耐心等待。';
                  break;
                case 'disabled':
                  message = '您的账户已被禁用，请联系管理员。';
                  break;
              }
            } else if (response.status === 401) {
              message = '用户名或密码错误，请重试。';
              passwordInput.value = '';
              passwordInput.focus();
            }

            throw new Error(message);
          }
          
          // 登录成功动画
          loginButton.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i> 登录成功';
          loginButton.classList.remove('btn-primary');
          loginButton.classList.add('btn-success');
          
          // 保存用户信息和令牌到本地存储
          AuthUtils.saveUserAndToken(data.user, data.token);
          
          // 短暂延迟后跳转，以便用户看到成功提示
          setTimeout(() => {
            // 跳转到首页
            window.location.href = '/';
          }, 200);
          
        } catch (error) {
          showError(error.message);
        } finally {
          showLoading(false);
        }
      });
      
      // 显示错误信息
      function showError(message) {
        errorMessage.textContent = message;
        loginError.classList.remove('d-none');
        // 添加轻微抖动效果
        loginError.classList.add('animate__animated', 'animate__shakeX');
        setTimeout(() => {
          loginError.classList.remove('animate__animated', 'animate__shakeX');
        }, 500);
      }
      
      // 显示/隐藏加载动画
      function showLoading(isLoading) {
        if (isLoading) {
          loginSpinner.classList.remove('d-none');
          loginButton.setAttribute('disabled', 'disabled');
        } else {
          loginSpinner.classList.add('d-none');
          loginButton.removeAttribute('disabled');
        }
      }
    });
  </script>
  
  <!-- 添加简单的页面加载动画 -->
  <script>
    window.addEventListener('load', () => {
      document.body.classList.add('loaded');
    });
  </script>
</body>
</html>