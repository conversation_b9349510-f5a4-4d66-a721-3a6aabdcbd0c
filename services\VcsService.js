// services/VcsService.js
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const path = require('path');
const fs = require('fs-extra');
const VcsCredential = require('../models/VcsCredential');

class VcsService {
  constructor() {
    this.tempDir = path.join(process.cwd(), 'temp', 'vcs');
    fs.ensureDirSync(this.tempDir);
  }

  /**
   * 克隆或检出仓库
   * @param {string} repositoryUrl - 仓库URL
   * @param {string} targetDir - 目标目录
   * @param {string} vcsType - VCS类型 (git/svn)
   * @param {string} credentialId - 凭据ID（可选）
   * @param {string} branch - 分支名（Git专用，可选）
   */
  async cloneRepository(repositoryUrl, targetDir, vcsType = 'git', credentialId = null, branch = 'main') {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
        if (!credential) {
          throw new Error('VCS凭据不存在');
        }
      }

      if (vcsType === 'git') {
        return await this.cloneGitRepository(repositoryUrl, targetDir, credential, branch);
      } else if (vcsType === 'svn') {
        return await this.checkoutSvnRepository(repositoryUrl, targetDir, credential);
      } else {
        throw new Error(`不支持的VCS类型: ${vcsType}`);
      }
    } catch (error) {
      throw new Error(`克隆仓库失败: ${error.message}`);
    }
  }

  /**
   * 更新仓库
   * @param {string} projectDir - 项目目录
   * @param {string} vcsType - VCS类型
   * @param {string} credentialId - 凭据ID（可选）
   */
  async updateRepository(repositoryUrl,projectDir, vcsType = 'git', credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      if (vcsType === 'git') {
        return await this.pullGitRepository(repositoryUrl,projectDir, credential);
      } else if (vcsType === 'svn') {
        return await this.updateSvnRepository(projectDir, credential);
      } else {
        throw new Error(`不支持的VCS类型: ${vcsType}`);
      }
    } catch (error) {
      throw new Error(`更新仓库失败: ${error.message}`);
    }
  }

  /**
   * 切换分支（Git专用）
   * @param {string} projectDir - 项目目录
   * @param {string} branch - 分支名
   * @param {string} credentialId - 凭据ID（可选）
   */
  async checkoutBranch(projectDir, branch, credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      // 检查是否为Git仓库
      const gitDir = path.join(projectDir, '.git');
      if (!(await fs.pathExists(gitDir))) {
        throw new Error('不是Git仓库，无法切换分支');
      }

      const result = await execPromise(`git checkout ${branch}`, { cwd: projectDir });
      return { success: true, stdout: result.stdout, stderr: result.stderr };
    } catch (error) {
      throw new Error(`切换分支失败: ${error.message}`);
    }
  }

  /**
   * 克隆Git仓库
   */
  async cloneGitRepository(repositoryUrl, targetDir, credential = null, branch = 'main') {
    let authUrl = repositoryUrl;
    
    if (credential) {
      // 构建带认证信息的URL
      const urlObj = new URL(repositoryUrl);
      urlObj.username = credential.username;
      urlObj.password = credential.password;
      authUrl = urlObj.toString();
    }

    const command = `git clone "${authUrl}" "${targetDir}"`;
    const result = await execPromise(command);
    
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 拉取Git仓库更新
   */
  async pullGitRepository(repositoryUrl,projectDir, credential = null) {
    let authUrl = repositoryUrl;
    
    if (credential) {
      // 构建带认证信息的URL
      const urlObj = new URL(repositoryUrl);
      urlObj.username = credential.username;
      urlObj.password = credential.password;
      authUrl = urlObj.toString();
    }

    const command = `git pull "${authUrl}"`;

    const result = await execPromise(command, { cwd: projectDir });
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 检出SVN仓库
   */
  async checkoutSvnRepository(repositoryUrl, targetDir, credential = null) {
    let command = `svn checkout "${repositoryUrl}" "${targetDir}"`;
    
    if (credential) {
      command += ` --username "${credential.username}" --password "${credential.password}" --non-interactive --trust-server-cert --trust-server-cert-failures=unknown-ca,cn-mismatch`;
    }

    const result = await execPromise(command);
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 更新SVN仓库
   */
  async updateSvnRepository(projectDir, credential = null) {
    let command = 'svn update';
    
    if (credential) {
      command += ` --username "${credential.username}" --password "${credential.password}" --non-interactive --trust-server-cert --trust-server-cert-failures=unknown-ca,cn-mismatch`;
    }

    const result = await execPromise(command, { cwd: projectDir });
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 检测仓库类型
   * @param {string} projectDir - 项目目录
   */
  async detectVcsType(projectDir) {
    try {
      const gitDir = path.join(projectDir, '.git');
      const svnDir = path.join(projectDir, '.svn');
      
      if (await fs.pathExists(gitDir)) {
        return 'git';
      } else if (await fs.pathExists(svnDir)) {
        return 'svn';
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取仓库信息
   * @param {string} projectDir - 项目目录
   * @param {string} vcsType - VCS类型
   */
  async getRepositoryInfo(projectDir, vcsType) {
    try {
      if (vcsType === 'git') {
        const remoteResult = await execPromise('git remote get-url origin', { cwd: projectDir });
        const branchResult = await execPromise('git branch --show-current', { cwd: projectDir });
        
        return {
          url: remoteResult.stdout.trim(),
          branch: branchResult.stdout.trim(),
          type: 'git'
        };
      } else if (vcsType === 'svn') {
        const infoResult = await execPromise('svn info --show-item url', { cwd: projectDir });
        
        return {
          url: infoResult.stdout.trim(),
          type: 'svn'
        };
      }
    } catch (error) {
      throw new Error(`获取仓库信息失败: ${error.message}`);
    }
  }

  async getSvnCommitLogs(projectDir, credentialId = null, limit = 10) {
    try {
      // 检查是否为SVN仓库
      const svnDir = path.join(projectDir, '.svn');
      if (!(await fs.pathExists(svnDir))) {
        throw new Error('不是SVN仓库，无法获取提交日志'); 
      }
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      // 获取日志，格式化为JSON
      const result = await execPromise(
        `svn log --username ${credential.username} --password ${credential.password} --limit ${limit} "${projectDir}"  --xml --non-interactive --trust-server-cert --trust-server-cert-failures=unknown-ca,cn-mismatch`,
        { cwd: projectDir } 
      );
      // 将XML日志转换为JSON
      const logs = this.parseSvnLogs(result.stdout);
      return logs;
    } catch (error) { 
      throw new Error(`获取SVN提交日志失败: ${error.message}`);
    }
  }

  /**
   * 解析SVN日志  
   * @param {string} xmlLogs - SVN日志的XML格式
   * @returns {Array} - 提交日志数组
   */
  async parseSvnLogs(xmlLogs) {
    const logs = [];
    const xml2js = require('xml2js');
    const parser = new xml2js.Parser(); 
    const parsedLogs = await parser.parseStringPromise(xmlLogs);
    const logEntries = parsedLogs.log.logentry || [];

    logEntries.forEach(entry => {
      const log = {
        hash: entry.$.revision[0],  
        author: entry.author[0],
        date: entry.date[0],
        message: entry.msg[0]
      };
      logs.push(log);
    });
    return logs;  
  }

  /**
   * 获取Git提交日志
   * @param {string} projectDir - 项目目录
   * @param {string} branch - 分支名
   * @param {number} limit - 最大日志数量
   * @returns {Promise<Array>} - 提交日志数组
   */
  async getGitCommitLogs(projectDir, branch, limit = 10) {
    try {
      // 检查是否为Git仓库
      const gitDir = path.join(projectDir, '.git');
      if (!(await fs.pathExists(gitDir))) {
        throw new Error('不是Git仓库，无法获取提交日志');
      }

      // 先执行git fetch确保有最新的分支信息
      try {
        await execPromise('git fetch --all', { cwd: projectDir });
      } catch (fetchError) {
        console.warn(`获取远程分支信息失败: ${fetchError.message}`);
        // 继续执行，因为本地可能已经有足够的信息
      }

      // 检查分支是否存在
      let branchExists = false;
      try {
        // 检查本地分支
        const localResult = await execPromise(`git branch --list ${branch}`, { cwd: projectDir });
        if (localResult.stdout.trim()) {
          branchExists = true;
        } else {
          // 检查远程分支
          const remoteResult = await execPromise(`git branch -r --list origin/${branch}`, { cwd: projectDir });
          if (remoteResult.stdout.trim()) {
            branchExists = true;
            branch = `origin/${branch}`; // 使用远程分支引用
          }
        }
      } catch (checkError) {
        console.warn(`检查分支存在性失败: ${checkError.message}`);
      }

      if (!branchExists) {
        throw new Error(`分支 '${branch}' 不存在`);
      }

      // 获取日志，格式化为JSON
      const result = await execPromise(
        `git log ${branch} -n ${limit} '--pretty=format:{"hash":"%h","author":"%an","date":"%ad","message":"%s"}'`, 
        { cwd: projectDir }
      );

      // 将每行解析为JSON对象
      const logs = result.stdout.split('\n')
        .filter(line => line.trim() !== '')
        .map(line => {
          try {
            try{
              return JSON.parse(line);
            }catch(e){
              // 定义正则表达式
              const pattern = /{hash:(\w+),author:(\w+),date:([^,]+),message:(.+?)}/;
              const match = line.match(pattern);

              if (match) {
                  const parsed = {
                      hash: match[1],
                      author: match[2],
                      date: match[3],
                      message: match[4]
                  };
                  return parsed;
              } else {
                return { hash: '', author: '', date: '', message: line };
              }
            }
          } catch (e) {
            return { hash: '', author: '', date: '', message: line };
          }
        });

      return logs;
    } catch (error) {
      throw new Error(`获取Git提交日志失败: ${error.message}`);
    }
  }

  /**
   * 获取两个分支或提交之间的差异日志
   * @param {string} projectDir - 项目目录
   * @param {string} fromRef - 起始引用（分支名或提交哈希）
   * @param {string} toRef - 结束引用（分支名或提交哈希）
   * @param {number} limit - 最大日志数量
   * @returns {Promise<Array>} - 提交日志数组
   */
  async getGitDiffLogs(projectDir, fromRef, toRef, limit = 20) {
    try {
      // 检查是否为Git仓库
      const gitDir = path.join(projectDir, '.git');
      if (!(await fs.pathExists(gitDir))) {
        throw new Error('不是Git仓库，无法获取差异日志');
      }

      // 先执行git fetch确保有最新的分支信息
      try {
        await execPromise('git fetch --all', { cwd: projectDir });
      } catch (fetchError) {
        console.warn(`获取远程分支信息失败: ${fetchError.message}`);
        // 继续执行，因为本地可能已经有足够的信息
      }

      // 检查和处理fromRef
      let validFromRef = fromRef;
      if (!this.isCommitHash(fromRef)) {
        // 如果不是提交哈希，检查是否为分支名
        try {
          const localResult = await execPromise(`git branch --list ${fromRef}`, { cwd: projectDir });
          if (!localResult.stdout.trim()) {
            // 检查远程分支
            const remoteResult = await execPromise(`git branch -r --list origin/${fromRef}`, { cwd: projectDir });
            if (remoteResult.stdout.trim()) {
              validFromRef = `origin/${fromRef}`;
            } else {
              throw new Error(`起始引用 '${fromRef}' 不存在`);
            }
          }
        } catch (checkError) {
          console.warn(`检查起始引用失败: ${checkError.message}`);
        }
      }

      // 检查和处理toRef
      let validToRef = toRef;
      if (!this.isCommitHash(toRef)) {
        // 如果不是提交哈希，检查是否为分支名
        try {
          const localResult = await execPromise(`git branch --list ${toRef}`, { cwd: projectDir });
          if (!localResult.stdout.trim()) {
            // 检查远程分支
            const remoteResult = await execPromise(`git branch -r --list origin/${toRef}`, { cwd: projectDir });
            if (remoteResult.stdout.trim()) {
              validToRef = `origin/${toRef}`;
            } else {
              throw new Error(`结束引用 '${toRef}' 不存在`);
            }
          }
        } catch (checkError) {
          console.warn(`检查结束引用失败: ${checkError.message}`);
        }
      }

      // 获取日志，格式化为JSON
      const logFormat = '--pretty=format:{"hash":"%h","author":"%an","date":"%ad","message":"%s"}';
      const result = await execPromise(
        `git log ${validFromRef}..${validToRef} -n ${limit} ${logFormat}`, 
        { cwd: projectDir }
      );

      // 将每行解析为JSON对象
      const logs = result.stdout.split('\n')
        .filter(line => line.trim() !== '')
        .map(line => {
          try {
            return JSON.parse(line);
          } catch (e) {
            return { hash: '', author: '', date: '', message: line };
          }
        });

      return logs;
    } catch (error) {
      throw new Error(`获取Git差异日志失败: ${error.message}`);
    }
  }

  /**
   * 检查字符串是否为Git提交哈希
   * @param {string} str - 要检查的字符串
   * @returns {boolean} - 是否为提交哈希
   */
  isCommitHash(str) {
    // Git提交哈希是40个十六进制字符，或者是缩写形式（至少4个字符）
    return /^[0-9a-f]{4,40}$/i.test(str);
  }

  /**
   * 测试VCS连接
   * @param {string} repositoryUrl - 仓库URL
   * @param {string} vcsType - VCS类型
   * @param {string} credentialId - 凭据ID（可选）
   */
  async testConnection(repositoryUrl, vcsType, credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }
      try {
        if (vcsType === 'git') {
          // 构建带认证信息的URL
          const urlObj = new URL(repositoryUrl);
          urlObj.username = credential.username;
          urlObj.password = credential.password;
          const authUrl = urlObj.toString();
          const result = await execPromise(
            `git ls-remote "${repositoryUrl}" ${authUrl}`);
        } else if (vcsType === 'svn') {
          const result = await execPromise(
            `svn info "${repositoryUrl}" --username ${credential.username} --password ${credential.password} --non-interactive --trust-server-cert --trust-server-cert-failures=unknown-ca,cn-mismatch`);
        }
        return { success: true, message: '连接测试成功' };
      } catch (error) {
        throw error;
      }
    } catch (error) {
      return { success: false, message: `连接测试失败` };
    }
  }
}

module.exports = new VcsService();
