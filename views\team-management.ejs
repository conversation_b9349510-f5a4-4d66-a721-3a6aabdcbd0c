<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>团队管理 - 前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/animate.min.css">
  <style>
    :root {
      --primary-color: #4e73df;
      --secondary-color: #6f42c1;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
      --warning-color: #f6c23e;
      --danger-color: #e74a3b;
      --light-color: #f8f9fc;
      --dark-color: #5a5c69;
      --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --transition-speed: 0.3s;
      --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --box-shadow-hover: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    body {
      background-color: #f8f9fc;
      font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    /* 滚动条美化 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    .sidebar {
      min-height: 100vh;
      background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      box-shadow: var(--card-shadow);
      position: sticky;
      top: 0;
      transition: all var(--transition-speed) ease-in-out;
      z-index: 100;
    }
    
    .nav-link {
      padding: 1rem 1.5rem;
      margin-bottom: 0.2rem;
      border-radius: 0.35rem;
      transition: all var(--transition-speed) ease-in-out;
      display: flex;
      align-items: center;
    }
    
    .nav-link:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }
    
    .nav-link.active {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 0.35rem;
      font-weight: 600;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .nav-link i {
      margin-right: 0.75rem;
      font-size: 1.1rem;
    }
    
    .content-area {
      padding: 1.5rem;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .team-card {
      border: none;
      border-radius: 0.5rem;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      background-color: #fff;
      box-shadow: var(--card-shadow);
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
      position: relative;
      overflow: hidden;
    }
    
    .team-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    .team-card.processing {
      pointer-events: none;
      opacity: 0.7;
    }
    
    .team-avatar {
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .team-card:hover .team-avatar {
      transform: scale(1.1);
    }
    
    .team-info {
      background-color: rgba(248, 249, 252, 0.5);
      border-radius: 0.5rem;
      padding: 0.75rem;
    }
    
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 10rem;
      font-weight: 600;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .team-card:hover .status-badge {
      transform: scale(1.05);
    }
    
    .stats-card {
      background: linear-gradient(135deg, #4e73df 0%, #6f42c1 100%);
      color: white;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }
    
    .stats-item {
      padding: 1rem;
      border-radius: 0.5rem;
      background-color: rgba(255, 255, 255, 0.1);
      transition: transform var(--transition-speed);
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
    }
    
    .stats-item:hover {
      transform: translateY(-5px);
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    .stats-item .icon-wrapper {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      color: white;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .stats-item:hover .icon-wrapper {
      transform: rotate(15deg);
    }
    
    .stats-item .icon-wrapper i {
      font-size: 1.25rem;
    }
    
    .stats-item .stats-info {
      flex: 1;
    }
    
    .stats-item .stats-info .stats-label {
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.25rem;
    }
    
    .stats-item .stats-info .stats-value {
      font-size: 1.25rem;
      font-weight: 600;
      color: white;
    }
    
    .btn {
      border-radius: 0.35rem;
      padding: 0.375rem 0.75rem;
      font-weight: 600;
      transition: all var(--transition-speed);
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn i {
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .btn:hover i {
      transform: scale(1.2);
    }
    
    .btn-group .btn {
      border-radius: 0.35rem;
      margin-right: 0.25rem;
    }
    
    .nav-tabs {
      border-bottom: 1px solid #dee2e6;
      margin-bottom: 0;
    }
    
    .nav-tabs .nav-link {
      border-radius: 0.35rem 0.35rem 0 0;
      font-weight: 600;
      padding: 0.75rem 1rem;
      transition: all var(--transition-speed) ease-in-out;
      color: var(--dark-color);
      border: 1px solid transparent;
      margin-bottom: -1px; /* 确保激活状态下边框覆盖tab-content上边框 */
    }
    
    .nav-tabs .nav-link:hover {
      background-color: rgba(78, 115, 223, 0.05);
      border-color: #e9ecef #e9ecef #dee2e6;
    }
    
    .nav-tabs .nav-link.active {
      color: var(--primary-color);
      background-color: #fff;
      border-color: #dee2e6 #dee2e6 #fff;
      font-weight: 700;
      position: relative;
      z-index: 2;
      box-shadow: 0 -3px 8px rgba(0, 0, 0, 0.05);
    }
    
    .nav-tabs .nav-link.active:after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #fff;
    }
    
    .nav-tabs .nav-link.active i {
      color: var(--primary-color);
    }
    
    .nav-tabs .nav-link i {
      margin-right: 0.5rem;
    }
    
    .tab-content {
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-top: none;
      border-radius: 0 0 0.35rem 0.35rem;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .tab-pane {
      transition: opacity 0.3s ease-in-out;
    }
    
    .tab-pane.fade {
      opacity: 0;
    }
    
    .tab-pane.fade.show {
      opacity: 1;
    }
    
    .modal-content {
      border: none;
      border-radius: 0.5rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }
    
    .modal-header {
      border-radius: 0.5rem 0.5rem 0 0;
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      color: white;
    }
    
    .modal-header .modal-title {
      font-weight: 700;
      display: flex;
      align-items: center;
    }
    
    .modal-header .modal-title i {
      margin-right: 0.5rem;
    }
    
    .modal-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .table {
      border-radius: 0.35rem;
      overflow: hidden;
    }
    
    .page-header {
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e3e6f0;
    }
    
    /* 动画类 */
    .animate__animated {
      animation-duration: 0.5s;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .action-buttons {
        width: 100%;
        margin-top: 1rem;
      }
      
      .team-card .d-flex {
        flex-direction: column;
      }
      
      .team-info {
        margin-top: 1rem;
      }
      
      .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
      }
      
      .tab-content {
        padding: 1rem !important;
      }
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 主内容区域 -->
      <main class="col-md-12 px-md-4 animate__animated animate__fadeIn">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4 page-header">
          <div>
            <h1 class="h2 mb-0"><i class="bi bi-diagram-3 me-2 text-primary"></i>团队管理</h1>
          </div>
          <div class="btn-toolbar">
            <button class="btn btn-outline-primary me-2" id="refresh-btn">
              <i class="bi bi-arrow-clockwise me-1"></i> 刷新数据
            </button>
            <button class="btn btn-primary" id="create-team-btn">
              <i class="bi bi-plus-circle me-1"></i> 创建团队
            </button>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="stats-card animate__animated animate__fadeInUp">
              <h5 class="mb-3"><i class="bi bi-graph-up me-2"></i>团队统计概览</h5>
              <div class="row g-3" id="team-stats">
                <!-- 统计数据将在这里动态加载 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 内容卡片 -->
        <div class="card shadow-sm border-0 animate__animated animate__fadeInUp animate__delay-1s">
          <div class="card-body p-0">
            <!-- 标签页 -->
            <ul class="nav nav-tabs px-3 pt-3" id="teamTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="my-teams-tab" data-bs-toggle="tab" data-bs-target="#my-teams" type="button" role="tab">
                  <i class="bi bi-users me-1"></i> 我的团队
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-teams-tab" data-bs-toggle="tab" data-bs-target="#all-teams" type="button" role="tab">
                  <i class="bi bi-building me-1"></i> 所有团队
                  <span class="badge bg-primary ms-1 text-white fw-bold" id="all-teams-count">0</span>
                </button>
              </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content p-4 p-md-5" id="teamTabContent">
              <!-- 我的团队 -->
              <div class="tab-pane fade show active animate__animated animate__fadeIn" id="my-teams" role="tabpanel">
                <div id="my-teams-container">
                  <!-- 我的团队列表将在这里动态加载 -->
                  <div class="text-center py-5 placeholder-glow">
                    <div class="spinner-border text-primary mb-3" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载团队数据...</p>
                  </div>
                </div>
              </div>

              <!-- 所有团队 -->
              <div class="tab-pane fade animate__animated animate__fadeIn" id="all-teams" role="tabpanel">
                <div id="all-teams-container">
                  <!-- 所有团队列表将在这里动态加载 -->
                  <div class="text-center py-5 placeholder-glow">
                    <div class="spinner-border text-primary mb-3" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载团队数据...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 创建团队模态框 -->
  <div class="modal fade" id="createTeamModal" tabindex="-1" aria-labelledby="createTeamModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createTeamModalLabel"><i class="bi bi-plus-circle me-2"></i>创建团队</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4">
          <form id="create-team-form">
            <div class="mb-4">
              <label for="team-name" class="form-label"><i class="bi bi-diagram-3 me-1"></i>团队名称</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-building"></i></span>
                <input type="text" class="form-control" id="team-name" placeholder="输入团队名称" required>
              </div>
              <small class="text-muted">团队名称将用于识别和展示，不可更改</small>
            </div>
            <div class="mb-4">
              <label for="team-description" class="form-label"><i class="bi bi-chat-left me-1"></i>团队描述</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-card-text"></i></span>
                <textarea class="form-control" id="team-description" rows="3" placeholder="输入团队描述信息"></textarea>
              </div>
              <small class="text-muted">描述团队的目的和职责</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" id="save-team-btn">
            <i class="bi bi-plus-circle me-1"></i>创建团队
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 团队详情模态框 -->
  <div class="modal fade" id="teamDetailModal" tabindex="-1" aria-labelledby="teamDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="teamDetailModalLabel"><i class="bi bi-diagram-3 me-2"></i>团队详情</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4" id="team-detail-content">
          <div class="p-4" id="team-detail-content">
            <div class="row mb-4" style="display: none;">
              <div class="col-md-4 text-center">
                <div class="rounded-circle bg-primary/10 w-24 h-24 mx-auto flex items-center justify-center mb-3">
                  <i class="bi bi-diagram-3 text-3xl text-primary"></i>
                </div>
                <h3 id="team-detail-name" class="text-xl font-bold text-gray-800"></h3>
              </div>
              <div class="col-md-8">
                <div class="bg-gray-50 p-4 rounded-lg h-full">
                  <h4 class="text-lg font-semibold mb-3 text-gray-700"><i class="bi bi-info-circle text-primary me-2"></i>团队信息</h4>
                  <div class="mb-2">
                    <p class="text-sm text-gray-500 mb-1"><i class="bi bi-chat-left text-primary me-1"></i>团队描述</p>
                    <p id="team-detail-description" class="text-gray-700"></p>
                  </div>
                  <div class="mb-2">
                    <p class="text-sm text-gray-500 mb-1"><i class="bi bi-calendar text-primary me-1"></i>创建时间</p>
                    <p id="team-detail-createdAt" class="text-gray-700"></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" id="teamDetailTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="members-tab" data-bs-toggle="tab" data-bs-target="#members" type="button" role="tab">
                  <i class="bi bi-people text-primary me-1"></i> 团队成员
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="projects-tab" data-bs-toggle="tab" data-bs-target="#projects" type="button" role="tab">
                  <i class="bi bi-boxes text-primary me-1"></i> 团队项目
                </button>
              </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content" id="teamDetailTabContent">
              <!-- 团队成员标签页 -->
              <div class="tab-pane fade show active" id="members" role="tabpanel">
                <div id="team-detail-members" class="overflow-x-auto">
                  <table class="table table-hover table-sm">
                    <thead>
                      <tr>
                        <th scope="col">成员名称</th>
                        <th scope="col">角色</th>
                        <th scope="col">加入时间</th>
                      </tr>
                    </thead>
                    <tbody id="team-members-list">
                      <!-- 成员列表将动态加载 -->
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- 团队项目标签页 -->
              <div class="tab-pane fade" id="projects" role="tabpanel" style="position:relative;">
                <div class="mb-4 d-flex justify-content-between align-items-center" style="position: absolute;right: 0px;top: -40px;">
                  <!-- <h5 class="text-md font-semibold text-gray-700"><i class="bi bi-boxes text-primary me-1"></i>团队项目列表</h5> -->
                  <button class="btn btn-primary btn-sm" id="add-project-to-team-btn"><i class="bi bi-plus-circle me-1"></i> 添加项目</button>
                </div>
                <div id="team-projects-container" class="overflow-x-auto">
                  <table class="table table-hover table-sm">
                    <thead>
                      <tr>
                        <th scope="col">项目名称</th>
                        <th scope="col">仓库URL</th>
                        <th scope="col">创建时间</th>
                        <th scope="col">操作</th>
                      </tr>
                    </thead>
                    <tbody id="team-projects-list">
                      <!-- 项目列表将动态加载 -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑团队模态框 -->
  <div class="modal fade" id="editTeamModal" tabindex="-1" aria-labelledby="editTeamModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editTeamModalLabel"><i class="bi bi-pencil me-2"></i>编辑团队</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4">
          <form id="edit-team-form">
            <input type="hidden" id="edit-team-id">
            <div class="mb-4">
              <label for="edit-team-name" class="form-label"><i class="bi bi-diagram-3 me-1"></i>团队名称</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-building"></i></span>
                <input type="text" class="form-control" id="edit-team-name" placeholder="输入团队名称" required>
              </div>
            </div>
            <div class="mb-4">
              <label for="edit-team-description" class="form-label"><i class="bi bi-chat-left me-1"></i>团队描述</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-card-text"></i></span>
                <textarea class="form-control" id="edit-team-description" rows="3" placeholder="输入团队描述信息"></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" id="save-edited-team-btn">
            <i class="bi bi-check-circle me-1"></i>保存修改
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 团队成员管理模态框 -->
  <div class="modal fade" id="teamMembersModal" tabindex="-1" aria-labelledby="teamMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="teamMembersModalLabel"><i class="bi bi-people-fill me-2"></i>团队成员管理</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4" id="team-members-content">
          <!-- 团队成员列表将在这里动态加载 -->
          <div class="text-center py-5 placeholder-glow">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted">正在加载团队成员数据...</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加项目到团队模态框 -->
  <div class="modal fade" id="addProjectToTeamModal" tabindex="-1" aria-labelledby="addProjectToTeamModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addProjectToTeamModalLabel"><i class="bi bi-plus-circle me-2"></i>添加项目到团队</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4">
          <form id="add-project-to-team-form">
            <input type="hidden" id="add-project-team-id">
            <div class="mb-4">
              <label for="project-select" class="form-label"><i class="bi bi-boxes me-1"></i>选择项目</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-list"></i></span>
                <select class="form-select" id="project-select" required>
                  <option value="">请选择项目</option>
                  <!-- 项目选项将动态加载 -->
                </select>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" id="confirm-add-project-btn">
            <i class="bi bi-check-circle me-1"></i>添加到团队
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 操作提示框 -->
  <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <!-- 成功提示框 -->
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="bi bi-check-circle me-2"></i><span id="successToastMessage">操作成功</span>
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>

    <!-- 错误提示框 -->
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="bi bi-exclamation-circle me-2"></i><span id="errorToastMessage">操作失败</span>
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/team-management.js"></script>
</body>
</html>