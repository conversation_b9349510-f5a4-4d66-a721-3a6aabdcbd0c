const CommandValidator = require('./CommandValidator');
const fs = require('fs-extra');
const path = require('path');
const winston = require('winston');

/**
 * 安全代理 - 负责处理所有安全相关的操作
 */
class SecurityAgent {
  constructor() {
    this.validator = new CommandValidator();
    this.setupLogger();
  }

  /**
   * 设置安全日志记录器
   */
  setupLogger() {
    // 确保日志目录存在
    fs.ensureDirSync('logs/security');

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      defaultMeta: { service: 'security-agent' },
      transports: [
        new winston.transports.File({ 
          filename: 'logs/security/injection.log',
          level: 'warn'
        }),
        new winston.transports.Console({
          format: winston.format.simple(),
          level: 'info'
        })
      ],
    });
  }

  /**
   * 验证构建命令是否安全
   * @param {string} command - 要验证的命令
   * @returns {boolean} - 是否安全
   */
  validateBuildCommand(command) {
    return this.validator.isCommandSafe(command);
  }

  /**
   * 净化构建命令，如果不安全则返回默认命令
   * @param {string} command - 要净化的命令
   * @param {string} defaultCommand - 默认命令
   * @returns {string} - 净化后的命令或默认命令
   */
  sanitizeBuildCommand(command, defaultCommand = 'npm run build') {
    return this.validator.isCommandSafe(command) ? command : defaultCommand;
  }

  /**
   * 记录命令注入尝试
   * @param {string} command - 被拒绝的命令
   * @param {object} context - 上下文信息（如用户ID、IP等）
   */
  logInjectionAttempt(command, context = {}) {
    this.logger.warn('检测到可能的命令注入尝试', {
      command,
      timestamp: new Date().toISOString(),
      ...context
    });
  }

  /**
   * 验证部署路径是否安全
   * @param {string} path - 要验证的路径
   * @returns {boolean} - 是否安全
   */
  validateDeployPath(path) {
    // 防止目录遍历攻击
    if (!path || typeof path !== 'string') return false;
    
    // 不允许使用 .. 进行目录遍历
    if (path.includes('..')) return false;
    
    // 不允许使用绝对路径（以/开头）
    if (path.startsWith('/')) {
      // 但允许常见的Web目录
      const safeRoots = ['/var/www', '/home', '/usr/local/www', '/data/www'];
      return safeRoots.some(root => path.startsWith(root));
    }
    
    return true;
  }

  /**
   * 验证URL是否安全
   * @param {string} url - 要验证的URL
   * @returns {boolean} - 是否安全
   */
  validateUrl(url) {
    if (!url || typeof url !== 'string') return false;
    
    try {
      const parsed = new URL(url);
      // 只允许http和https协议
      return ['http:', 'https:'].includes(parsed.protocol);
    } catch (e) {
      return false;
    }
  }

  /**
   * 验证服务器连接信息是否安全
   * @param {object} serverInfo - 服务器信息
   * @returns {boolean} - 是否安全
   */
  validateServerInfo(serverInfo) {
    if (!serverInfo || typeof serverInfo !== 'object') return false;
    
    // 验证主机名
    if (!serverInfo.host || typeof serverInfo.host !== 'string') return false;
    
    // 验证端口号
    if (serverInfo.port) {
      const port = parseInt(serverInfo.port);
      if (isNaN(port) || port < 1 || port > 65535) return false;
    }
    
    // 验证用户名
    if (!serverInfo.username || typeof serverInfo.username !== 'string') return false;
    
    return true;
  }

  /**
   * 检测并处理潜在的安全威胁
   * @param {object} data - 要检查的数据
   * @param {object} options - 检查选项
   * @returns {object} - 处理结果
   */
  detectThreats(data, options = {}) {
    const threats = [];
    
    // 检查命令注入
    if (data.command && !this.validateBuildCommand(data.command)) {
      threats.push({
        type: 'command-injection',
        field: 'command',
        value: data.command
      });
    }
    
    // 检查路径遍历
    if (data.path && !this.validateDeployPath(data.path)) {
      threats.push({
        type: 'path-traversal',
        field: 'path',
        value: data.path
      });
    }
    
    // 检查URL注入
    if (data.url && !this.validateUrl(data.url)) {
      threats.push({
        type: 'url-injection',
        field: 'url',
        value: data.url
      });
    }
    
    // 如果发现威胁，记录日志
    if (threats.length > 0) {
      this.logger.warn('检测到安全威胁', {
        threats,
        data,
        options,
        timestamp: new Date().toISOString()
      });
    }
    
    return {
      safe: threats.length === 0,
      threats
    };
  }
}

// 导出单例实例
module.exports = new SecurityAgent();