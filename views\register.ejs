<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端部署工具 - 注册</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/animate.min.css">
  <link rel="stylesheet" href="/css/register-modern.css">
</head>
<body>
  <!-- 页面加载动画 -->
  <div class="page-loader" id="page-loader">
    <div class="loader-spinner"></div>
    <div class="loader-text">正在加载...</div>
  </div>
  
  <main class="form-register">
    <div class="text-center mb-4">
      <div class="register-logo">
        <i class="bi bi-cloud-upload"></i>
      </div>
      <h1 class="h2 mb-2 fw-bold">前端部署工具</h1>
      <!-- <div class="badge bg-primary bg-gradient mb-3 px-3 py-2 rounded-pill">
        <i class="bi bi-code-slash me-1"></i> 高效 · 便捷
      </div>
      <p class="text-muted mb-4">创建您的新账号</p> -->
    </div>
    
    <div class="card">
      <div class="card-body">
        <h5 class="card-title text-center mb-4">创建新账号</h5>
        <form id="register-form">
          <div class="alert alert-danger d-none" id="register-error">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span id="error-message"></span>
          </div>
          
          <div class="form-group mb-3">
            <label for="username" class="form-label"><i class="bi bi-person"></i> 用户名</label>
            <input type="text" class="form-control" id="username" required autocomplete="username">
            <div class="form-text">用户名将用于登录系统</div>
            <div class="invalid-feedback">请输入有效的用户名</div>
          </div>
          
          <div class="form-group mb-3">
            <label for="displayName" class="form-label"><i class="bi bi-person-badge"></i> 显示名称</label>
            <input type="text" class="form-control" id="displayName">
            <div class="form-text">将显示在界面上的名称</div>
          </div>
          
          <div class="form-group mb-3">
            <label for="email" class="form-label"><i class="bi bi-envelope"></i> 电子邮箱</label>
            <input type="email" class="form-control" id="email" required autocomplete="email">
            <div class="invalid-feedback">请输入有效的电子邮箱地址</div>
          </div>
          
          <div class="form-group mb-3">
            <label for="password" class="form-label"><i class="bi bi-lock"></i> 密码</label>
            <div class="password-field-wrapper">
              <input type="password" class="form-control" id="password" required autocomplete="new-password">
              <button type="button" class="password-toggle" id="password-toggle">
                <i class="bi bi-eye"></i>
              </button>
            </div>
            <div class="password-strength">
              <div class="password-strength-bar" id="password-strength-bar"></div>
            </div>
            <div class="password-strength-text" id="password-strength-text">请输入密码</div>
            <div class="form-text">请使用强密码</div>
            <div class="invalid-feedback">密码长度至少为8个字符</div>
          </div>
          
          <div class="form-group mb-4">
            <label for="confirmPassword" class="form-label"><i class="bi bi-shield-lock"></i> 确认密码</label>
            <div class="password-field-wrapper">
              <input type="password" class="form-control" id="confirmPassword" required autocomplete="new-password">
              <button type="button" class="password-toggle" id="confirm-password-toggle">
                <i class="bi bi-eye"></i>
              </button>
            </div>
            <div class="invalid-feedback">两次输入的密码不一致</div>
          </div>
          
          <button class="w-100 btn btn-lg btn-primary" type="submit" id="register-button">
            <span class="btn-text"><i class="bi bi-person-plus me-2"></i> 创建账号</span>
            <span class="spinner-border spinner-border-sm d-none" id="register-spinner" role="status" aria-hidden="true"></span>
          </button>
        </form>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <p>已有账号？ <a href="/login" class="fw-bold"><i class="bi bi-box-arrow-in-right"></i> 立即登录</a></p>
    </div>
    
    <div class="footer">
      <p>© 2025 前端部署工具 | 仅供精准教学组内部使用</p>
    </div>
  </main>
  
  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // 页面加载动画
      const pageLoader = document.getElementById('page-loader');
      setTimeout(() => {
        pageLoader.style.opacity = '0';
        setTimeout(() => {
          pageLoader.style.display = 'none';
          document.body.classList.add('loaded');
        }, 500);
      }, 800);
      
      // 获取表单元素
      const registerForm = document.getElementById('register-form');
      const registerError = document.getElementById('register-error');
      const errorMessage = document.getElementById('error-message');
      const registerButton = document.getElementById('register-button');
      const registerSpinner = document.getElementById('register-spinner');
      
      const usernameInput = document.getElementById('username');
      const displayNameInput = document.getElementById('displayName');
      const emailInput = document.getElementById('email');
      const passwordInput = document.getElementById('password');
      const confirmPasswordInput = document.getElementById('confirmPassword');
      
      const passwordToggle = document.getElementById('password-toggle');
      const confirmPasswordToggle = document.getElementById('confirm-password-toggle');
      const passwordStrengthBar = document.getElementById('password-strength-bar');
      const passwordStrengthText = document.getElementById('password-strength-text');
      
      // 自动聚焦用户名输入框
      setTimeout(() => {
        usernameInput.focus();
      }, 1000);
      
      // 添加输入框动画效果
      [usernameInput, displayNameInput, emailInput, passwordInput, confirmPasswordInput].forEach(input => {
        input.addEventListener('focus', () => {
          input.parentElement.classList.add('shadow-sm');
          input.closest('.form-group').classList.add('focused');
        });
        
        input.addEventListener('blur', () => {
          input.parentElement.classList.remove('shadow-sm');
          input.closest('.form-group').classList.remove('focused');
        });
      });
      
      // 密码可见性切换
      passwordToggle.addEventListener('click', () => {
        togglePasswordVisibility(passwordInput, passwordToggle);
      });
      
      confirmPasswordToggle.addEventListener('click', () => {
        togglePasswordVisibility(confirmPasswordInput, confirmPasswordToggle);
      });
      
      function togglePasswordVisibility(input, button) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        button.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
      }
      
      // 密码强度检测
      passwordInput.addEventListener('input', () => {
        const password = passwordInput.value;
        const strength = checkPasswordStrength(password);
        
        // 更新密码强度指示器
        passwordStrengthBar.className = 'password-strength-bar';
        passwordStrengthText.className = 'password-strength-text';
        
        if (password.length === 0) {
          passwordStrengthBar.style.width = '0';
          passwordStrengthText.textContent = '请输入密码';
        } else if (strength === 1) {
          passwordStrengthBar.classList.add('strength-weak');
          passwordStrengthText.textContent = '弱';
          passwordStrengthText.style.color = 'var(--danger-color)';
        } else if (strength === 2) {
          passwordStrengthBar.classList.add('strength-medium');
          passwordStrengthText.textContent = '中等';
          passwordStrengthText.style.color = 'var(--warning-color)';
        } else if (strength === 3) {
          passwordStrengthBar.classList.add('strength-strong');
          passwordStrengthText.textContent = '强';
          passwordStrengthText.style.color = 'var(--accent-color)';
        } else if (strength === 4) {
          passwordStrengthBar.classList.add('strength-very-strong');
          passwordStrengthText.textContent = '非常强';
          passwordStrengthText.style.color = 'var(--success-color)';
        }
      });
      
      // 实时验证确认密码
      confirmPasswordInput.addEventListener('input', () => {
        if (passwordInput.value && confirmPasswordInput.value) {
          if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.classList.add('is-invalid');
          } else {
            confirmPasswordInput.classList.remove('is-invalid');
            confirmPasswordInput.classList.add('is-valid');
          }
        }
      });
      
      // 实时验证邮箱格式
      emailInput.addEventListener('input', () => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailInput.value && !emailRegex.test(emailInput.value)) {
          emailInput.classList.add('is-invalid');
        } else if (emailInput.value) {
          emailInput.classList.remove('is-invalid');
          emailInput.classList.add('is-valid');
        } else {
          emailInput.classList.remove('is-invalid', 'is-valid');
        }
      });
      
      // 表单提交处理
      registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const displayName = displayNameInput.value.trim() || username;
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        // 表单验证
        let isValid = true;
        
        // 用户名验证
        if (!username || username.length < 3) {
          usernameInput.classList.add('is-invalid');
          isValid = false;
        } else {
          usernameInput.classList.remove('is-invalid');
        }
        
        // 邮箱验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email || !emailRegex.test(email)) {
          emailInput.classList.add('is-invalid');
          isValid = false;
        } else {
          emailInput.classList.remove('is-invalid');
        }
        
        // 密码验证
        if (!password || password.length < 8) {
          passwordInput.classList.add('is-invalid');
          isValid = false;
        } else {
          passwordInput.classList.remove('is-invalid');
        }
        
        // 确认密码验证
        if (password !== confirmPassword) {
          confirmPasswordInput.classList.add('is-invalid');
          isValid = false;
        } else {
          confirmPasswordInput.classList.remove('is-invalid');
        }
        
        if (!isValid) {
          showError('请修正表单中的错误');
          return;
        }
        
        try {
          // 显示加载动画
          showLoading(true);
          registerError.classList.add('d-none');
          
          const response = await fetch('/api/users/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, displayName, email, password })
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            throw new Error(data.error || '注册失败');
          }
          
          // 注册成功动画
          registerButton.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i> 注册成功';
          registerButton.classList.remove('btn-primary');
          registerButton.classList.add('btn-success');
          
          // 短暂延迟后跳转，以便用户看到成功提示
          setTimeout(() => {
            // 跳转到登录页面
            window.location.href = '/login';
          }, 1000);
          
        } catch (error) {
          showError(error.message);
        } finally {
          showLoading(false);
        }
      });
      
      // 显示错误信息
      function showError(message) {
        errorMessage.textContent = message;
        registerError.classList.remove('d-none');
        // 添加轻微抖动效果
        registerError.classList.add('animate__animated', 'animate__shakeX');
        setTimeout(() => {
          registerError.classList.remove('animate__animated', 'animate__shakeX');
        }, 500);
      }
      
      // 显示/隐藏加载动画
      function showLoading(isLoading) {
        if (isLoading) {
          registerSpinner.classList.remove('d-none');
          registerButton.classList.add('btn-loading');
          registerButton.setAttribute('disabled', 'disabled');
        } else {
          registerSpinner.classList.add('d-none');
          registerButton.classList.remove('btn-loading');
          registerButton.removeAttribute('disabled');
        }
      }
      
      // 密码强度检测函数
      function checkPasswordStrength(password) {
        if (!password) return 0;
        
        let strength = 0;
        
        // 长度检查
        if (password.length >= 6) strength += 1;
        if (password.length >= 10) strength += 1;
        
        // 复杂度检查
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        
        // 最终强度评分
        if (strength <= 2) return 1; // 弱
        if (strength <= 4) return 2; // 中等
        if (strength <= 6) return 3; // 强
        return 4; // 非常强
      }
    });
  </script>
  
  <!-- 添加简单的页面加载动画 -->
  <script>
    window.addEventListener('load', () => {
      document.body.classList.add('loaded');
    });
  </script>
</body>
</html>