const express = require('express');
const router = express.Router();
const Team = require('../models/Team');
const User = require('../models/User');
const Project = require('../models/Project');
const { authenticateJWT, requireAdmin } = require('../middleware/auth');

// 获取所有团队 (管理员) / 获取用户所属团队 (普通用户)
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const user = await User.getById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 管理员可以查看所有团队
    if (user.role === 'admin') {
      const teams = await Team.getAll();
      return res.json(teams);
    }

    // 普通用户只能查看自己所属的团队
    const teams = await Team.getTeamsByUserId(req.user.id);
    res.json(teams);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 获取团队统计信息
router.get('/stats', authenticateJWT, async (req, res) => {
  try {
    // 只有管理员可以获取所有团队统计
    const user = await User.getById(req.user.id);
    if (user.role !== 'admin') {
      // 普通用户只能获取自己的团队统计
      const myTeamsCount = (await Team.getTeamsByUserId(req.user.id)).length;
      return res.json({
        myTeamsCount
      });
    }

    const stats = await Team.getStats();

    // 获取我的团队数量
    const myTeamsCount = (await Team.getTeamsByUserId(req.user.id)).length;

    res.json({
      ...stats,
      myTeamsCount
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 获取团队总数
router.get('/count', authenticateJWT, async (req, res) => {
  try {
    const stats = await Team.getStats();
    res.json({
      count: stats.totalTeams
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 获取当前用户所属的团队
router.get('/my-teams', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    const teams = await Team.getTeamsByUserId(userId);
    res.json(teams);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});



// 获取单个团队
router.get('/:id', authenticateJWT, async (req, res) => {
  try {
    const team = await Team.getById(req.params.id);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查用户是否有权限查看此团队
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId && !((team.memberIds || []).includes(userId))) {
      return res.status(403).json({ error: '没有权限查看此团队' });
    }

    res.json(team);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建团队
router.post('/', authenticateJWT, async (req, res) => {
  try {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ message: '团队名称不能为空' });
    }

    const teamData = {
      name,
      description,
      ownerId: req.user.id
    };

    const newTeam = await Team.create(teamData);

    // 更新用户的teamIds字段
    await User.addToTeam(req.user.id, newTeam.id);

    res.status(201).json(newTeam);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 更新团队
router.put('/:id', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;
    const teamData = req.body;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查用户是否有权限更新此团队
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId) {
      return res.status(403).json({ error: '没有权限更新此团队' });
    }

    const updatedTeam = await Team.update(teamId, teamData);
    res.json(updatedTeam);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除团队
router.delete('/:id', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查用户是否有权限删除此团队
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId) {
      return res.status(403).json({ error: '没有权限删除此团队' });
    }

    // 删除团队前，先将关联的项目teamId设置为空
    const projects = await Project.getByTeamId(teamId);
    for (const project of projects) {
      await Project.update(project.id, { teamId: '' });
    }

    await Team.delete(teamId);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 添加成员到团队
router.post('/:id/members', authenticateJWT, async (req, res) => {
  try {
    const { userId, username } = req.body;

    // 如果提供了username但没有userId，根据username查询userId
    let targetUserId = userId;
    if (!targetUserId && username) {
      const userByUsername = await User.getByUsername(username);
      if (!userByUsername) {
        return res.status(404).json({ message: '找不到该用户名的用户' });
      }
      targetUserId = userByUsername.id;
    }

    if (!targetUserId) {
      return res.status(400).json({ message: '用户ID不能为空，请提供userId或username' });
    }

    const team = await Team.getById(req.params.id);
    if (!team) {
      return res.status(404).json({ message: '团队不存在' });
    }

    // 检查权限：只有团队所有者或管理员可以添加成员
    const user = await User.getById(req.user.id);
    if (user.role !== 'admin' && team.ownerId !== req.user.id) {
      return res.status(403).json({ message: '无权添加成员' });
    }

    // 检查用户是否存在
    const targetUser = await User.getById(targetUserId);
    if (!targetUser) {
      return res.status(404).json({ message: '目标用户不存在' });
    }

    // 添加成员到团队
    // await Team.addMember(req.params.id, targetUserId);

    // 更新用户的teamIds字段
    await User.addToTeam(targetUserId, req.params.id);

    res.json({ success: true, message: '成员已添加' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 获取团队成员
router.get('/:id/members', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查用户是否有权限查看此团队的成员
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId && !((team.memberIds || []).includes(userId))) {
      return res.status(403).json({ error: '没有权限查看此团队的成员' });
    }

    // 获取团队成员
    const members = await Team.getMembers(teamId);
    res.json(members);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 从团队移除成员
router.delete('/:id/members/:userId', authenticateJWT, async (req, res) => {
  try {
    const team = await Team.getById(req.params.id);
    if (!team) {
      return res.status(404).json({ message: '团队不存在' });
    }

    // 检查权限：只有团队所有者或管理员可以移除成员
    const user = await User.getById(req.user.id);
    if (user.role !== 'admin' && team.ownerId !== req.user.id) {
      return res.status(403).json({ message: '无权移除成员' });
    }

    // 不能移除自己（如果是所有者）
    if (team.ownerId === req.user.id && req.user.id === req.params.userId) {
      return res.status(400).json({ message: '团队所有者不能移除自己' });
    }

    // 移除成员
    // await Team.removeMember(req.params.id, req.params.userId);

    // 更新用户的teamIds字段
    await User.removeFromTeam(req.params.userId, req.params.id);

    res.json({ success: true, message: '成员已移除' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 添加项目到团队
router.post('/:id/projects', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;
    const { projectId } = req.body;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }

    // 检查用户是否有权限将项目添加到团队
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId) {
      return res.status(403).json({ error: '没有权限将项目添加到此团队' });
    }

    // 更新项目的teamId
    await Project.update(projectId, { teamId });

    res.json({ success: true, message: '项目已成功添加到团队' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 从团队移除项目
router.delete('/:id/projects/:projectId', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;
    const projectId = req.params.projectId;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }

    // 检查用户是否有权限从团队移除项目
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId) {
      return res.status(403).json({ error: '没有权限从团队移除项目' });
    }

    // 检查项目是否属于该团队
    if (project.teamId !== teamId) {
      return res.status(400).json({ error: '项目不属于该团队' });
    }

    // 更新项目的teamId为空
    await Project.update(projectId, { teamId: '' });

    res.json({ success: true, message: '项目已成功从团队移除' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取团队中的项目
router.get('/:id/projects', authenticateJWT, async (req, res) => {
  try {
    const teamId = req.params.id;

    // 检查团队是否存在
    const team = await Team.getById(teamId);
    if (!team) {
      return res.status(404).json({ error: '团队不存在' });
    }

    // 检查用户是否有权限查看此团队的项目
    const userId = req.user.id;
    if (req.user.role !== 'admin' && team.ownerId !== userId && !((team.memberIds || []).includes(userId))) {
      return res.status(403).json({ error: '没有权限查看此团队的项目' });
    }

    const projects = await Project.getByTeamId(teamId);
    res.json(projects);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;