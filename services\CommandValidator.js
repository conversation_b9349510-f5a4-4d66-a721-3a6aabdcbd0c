/**
 * 命令验证器 - 负责验证命令的安全性
 */
class CommandValidator {
    constructor() {
      // 允许的命令前缀白名单
      this.ALLOWED_COMMAND_PREFIXES = [
        'npm',
        'npm run',
        'npm ci',
        'npm install',
        'npm build',
        'yarn',
        'yarn install',
        'yarn build',
        'pnpm',
        'pnpm install',
        'pnpm build',
        'node',
        'npx'
      ];
  
      // 危险的命令模式黑名单
      this.DANGEROUS_PATTERNS = [
        // 文件操作
        /rm\s+-rf/i,      // 删除文件
        /rmdir/i,         // 删除目录
        /unlink/i,        // 删除文件
        /touch/i,         // 创建文件
        
        // 命令链接和执行
        /;\s*\w+/i,       // 命令分隔符后跟命令
        // /&&\s*\w+/i,      // 命令链接后跟命令
        /\|\|\s*\w+/i,    // 命令条件执行后跟命令
        /`[^`]*`/g,       // 命令替换（反引号）
        /\$\([^)]*\)/g,   // 命令替换（$()）
        
        // 重定向和管道
        />/g,             // 输出重定向
        /</g,             // 输入重定向
        /\|(?!\|)/g,      // 管道符（但允许 ||）
        
        // 网络命令
        /wget/i,          // 下载命令
        /curl/i,          // 下载命令
        /nc\s/i,          // netcat
        /telnet/i,        // telnet
        /ssh/i,           // ssh
        
        // JavaScript危险函数
        /eval\(/i,        // eval执行
        /Function\(/i,    // 动态函数创建
        /process\.env/i,  // 访问环境变量
        /require\(/i,     // 动态require
        /import\(/i,      // 动态import
        
        // 子进程相关
        /child_process/i, // 子进程模块
        /exec\(/i,        // 执行命令
        /spawn\(/i,       // 生成进程
        /fork\(/i,        // 派生进程
        
        // 基本命令分隔符
        /;/,              // 命令分隔符
        // /&&/,             // 命令连接符
        /\|\|/,           // 命令条件执行符
        
        // 其他危险字符
        /\\/,             // 转义字符
        // /\$/,             // 变量引用
        /%/,              // 可能用于编码绕过
        
        // 系统命令
        /sudo/i,          // sudo
        /su\s/i,          // su
        /chmod/i,         // 修改权限
        /chown/i,         // 修改所有者
        /passwd/i,        // 密码操作
        /mkfs/i,          // 格式化文件系统
        /dd\s/i,          // 磁盘操作
        /fdisk/i,         // 磁盘分区
        
        // Shell特殊字符
        /\n/,             // 换行符
        /\r/,             // 回车符
        /\t/,             // 制表符
        
        // 其他危险命令
        /shutdown/i,      // 关机
        /reboot/i,        // 重启
        /halt/i,          // 停止
        /init\s+0/i,      // 关机
        /poweroff/i       // 关机
      ];
    }
  
    /**
     * 验证命令是否安全
     * @param {string} command - 要验证的命令
     * @returns {boolean} - 是否安全
     */
    isCommandSafe(command) {
      if (!command || typeof command !== 'string') {
        return false;
      }
  
      // 检查命令是否以允许的前缀开头
      const isAllowedPrefix = this.ALLOWED_COMMAND_PREFIXES.some(prefix => 
        command.trim().startsWith(prefix)
      );
  
      if (!isAllowedPrefix) {
        return false;
      }
  
      // 检查是否包含危险模式
      const hasDangerousPattern = this.DANGEROUS_PATTERNS.some(pattern => 
        pattern.test(command)
      );
  
      return !hasDangerousPattern;
    }
  
    /**
     * 获取命令中的危险模式
     * @param {string} command - 要检查的命令
     * @returns {Array} - 发现的危险模式列表
     */
    getDangerousPatterns(command) {
      if (!command || typeof command !== 'string') {
        return [];
      }
  
      return this.DANGEROUS_PATTERNS
        .filter(pattern => pattern.test(command))
        .map(pattern => pattern.toString());
    }
  
    /**
     * 尝试修复命令中的安全问题
     * @param {string} command - 要修复的命令
     * @returns {object} - 修复结果
     */
    repairCommand(command) {
      if (!command || typeof command !== 'string') {
        return { 
          success: false, 
          command: 'npm run build',
          original: command,
          issues: ['空命令']
        };
      }
  
      // 检查命令前缀
      const prefix = this.ALLOWED_COMMAND_PREFIXES.find(p => command.trim().startsWith(p));
      if (!prefix) {
        return { 
          success: false, 
          command: 'npm run build',
          original: command,
          issues: ['不允许的命令前缀']
        };
      }
  
      // 获取危险模式
      const dangerousPatterns = this.getDangerousPatterns(command);
      if (dangerousPatterns.length === 0) {
        return { success: true, command, original: command, issues: [] };
      }
  
      // 尝试移除危险字符
      let repairedCommand = command;
      const issues = [];
  
      dangerousPatterns.forEach(pattern => {
        issues.push(`包含危险模式: ${pattern}`);
        
        // 简单替换一些常见的危险字符
        repairedCommand = repairedCommand
          .replace(/;/g, '')
          .replace(/&&/g, '')
          .replace(/\|\|/g, '')
          .replace(/>/g, '')
          .replace(/</g, '')
          .replace(/\|/g, '')
          .replace(/`/g, '')
          .replace(/\$/g, '')
          .replace(/\\/g, '');
      });
  
      // 如果修复后的命令不再包含危险模式，则返回修复后的命令
      if (!this.getDangerousPatterns(repairedCommand).length) {
        return { 
          success: true, 
          command: repairedCommand, 
          original: command,
          issues
        };
      }
  
      // 如果无法修复，则返回默认命令
      return { 
        success: false, 
        command: 'npm run build', 
        original: command,
        issues
      };
    }
  }
  
  module.exports = CommandValidator;