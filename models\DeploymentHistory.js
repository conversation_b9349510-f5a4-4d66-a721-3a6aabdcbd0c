const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class DeploymentHistory {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'configs');
    this.historyFile = path.join(this.configDir, 'deployment_history.json');
    fs.ensureDirSync(this.configDir);
    // 确保历史文件存在
    if (!(fs.existsSync(this.historyFile))) {
      fs.writeJsonSync(this.historyFile, [], { spaces: 2 });
    }
  }

  // 记录部署历史
  async recordDeployment(projectId, serverId, archiveFileName, branch, deploymentTime = new Date()) {
    try {
      const history = await fs.readJson(this.historyFile);
      const record = {
        id: uuidv4(),
        projectId,
        serverId,
        archiveFileName,
        branch,
        deploymentTime: deploymentTime.toISOString(),
        restorePoint: true // 标记为可还原点
      };
      history.push(record);
      await fs.writeJson(this.historyFile, history, { spaces: 2 });
      return record;
    } catch (error) {
      throw new Error(`记录部署历史失败: ${error.message}`);
    }
  }

  // 获取项目在特定服务器上的部署历史
  async getDeploymentHistory(projectId, serverId) {
    try {
      const history = await fs.readJson(this.historyFile);
      return history
        .filter(record => record.projectId === projectId && record.serverId === serverId)
        .sort((a, b) => new Date(b.deploymentTime) - new Date(a.deploymentTime));
    } catch (error) {
      throw new Error(`获取部署历史失败: ${error.message}`);
    }
  }

  // 获取所有项目的部署历史
  async getAllDeploymentHistory() {
    try {
      const history = await fs.readJson(this.historyFile);
      return history.sort((a, b) => new Date(b.deploymentTime) - new Date(a.deploymentTime));
    } catch (error) {
      throw new Error(`获取所有部署历史失败: ${error.message}`);
    }
  }
}

module.exports = new DeploymentHistory();