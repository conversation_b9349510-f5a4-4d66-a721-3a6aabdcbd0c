const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const Project = require('./Project');
const Server = require('./Server');
const User = require('./User');

class BuildQueue {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'queue');
    fs.ensureDirSync(this.configDir);
    this.queueFile = path.join(this.configDir, 'queue.json');
    this.historyFile = path.join(this.configDir, 'history.json');
    this.initQueueFiles();
    
    // 最大并行构建数
    this.maxConcurrentBuilds = 3;
    
    // 当前正在执行的构建
    this.activeBuilds = [];
  }

  // 初始化队列文件
  async initQueueFiles() {
    try {
      if (!(await fs.pathExists(this.queueFile))) {
        await fs.writeJson(this.queueFile, { queue: [] }, { spaces: 2 });
      }
      
      if (!(await fs.pathExists(this.historyFile))) {
        await fs.writeJson(this.historyFile, { history: [] }, { spaces: 2 });
      }
    } catch (error) {
      throw new Error(`初始化构建队列文件失败: ${error.message}`);
    }
  }

  // 获取当前队列
  async getQueue() {
    try {
      const data = await fs.readJson(this.queueFile);
      return data.queue;
    } catch (error) {
      throw new Error(`获取构建队列失败: ${error.message}`);
    }
  }

  // 获取构建历史
  async getHistory(limit = 20) {
    try {
      const data = await fs.readJson(this.historyFile);
      return data.history.slice(0, limit);
    } catch (error) {
      throw new Error(`获取构建历史失败: ${error.message}`);
    }
  }

  // 添加构建任务到队列
  async addToQueue(buildData) {
    try {
      const data = await fs.readJson(this.queueFile);
      
      // 验证项目存在
      const project = await Project.getById(buildData.projectId);
      if (!project) {
        throw new Error('项目不存在');
      }
      
      // 创建构建任务
      const buildId = uuidv4();
      const build = {
        id: buildId,
        projectId: buildData.projectId,
        projectName: project.name,
        branch: buildData.branch,
        userId: buildData.userId,
        username: buildData.username,
        servers: buildData.servers || project.servers,
        status: 'queued', // queued, running, success, failed
        queuedAt: new Date().toISOString(),
        startedAt: null,
        completedAt: null,
        logs: []
      };
      
      // 添加到队列
      data.queue.push(build);
      await fs.writeJson(this.queueFile, data, { spaces: 2 });
      
      return build;
    } catch (error) {
      throw new Error(`添加构建任务失败: ${error.message}`);
    }
  }

  // 更新构建状态
  async updateBuildStatus(buildId, status, log = null) {
    try {
      const queueData = await fs.readJson(this.queueFile);
      const buildIndex = queueData.queue.findIndex(build => build.id === buildId);
      
      if (buildIndex === -1) {
        throw new Error('构建任务不存在');
      }
      
      const build = queueData.queue[buildIndex];
      build.status = status;
      
      // 添加日志
      if (log) {
        build.logs.push({
          time: new Date().toISOString(),
          message: log
        });
      }
      
      // 更新开始或完成时间
      if (status === 'running' && !build.startedAt) {
        build.startedAt = new Date().toISOString();
      } else if (['success', 'failed'].includes(status) && !build.completedAt) {
        build.completedAt = new Date().toISOString();
        
        // 将完成的构建移到历史记录
        const historyData = await fs.readJson(this.historyFile);
        historyData.history.unshift(build); // 添加到历史记录的开头
        
        // 限制历史记录数量
        if (historyData.history.length > 100) {
          historyData.history = historyData.history.slice(0, 100);
        }
        
        await fs.writeJson(this.historyFile, historyData, { spaces: 2 });
        
        // 从队列中移除
        queueData.queue.splice(buildIndex, 1);
      }
      
      await fs.writeJson(this.queueFile, queueData, { spaces: 2 });
      return build;
    } catch (error) {
      throw new Error(`更新构建状态失败: ${error.message}`);
    }
  }

  // 获取构建详情
  async getBuildById(buildId) {
    try {
      // 先在队列中查找
      const queueData = await fs.readJson(this.queueFile);
      let build = queueData.queue.find(build => build.id === buildId);
      
      // 如果队列中没有，则在历史记录中查找
      if (!build) {
        const historyData = await fs.readJson(this.historyFile);
        build = historyData.history.find(build => build.id === buildId);
      }
      
      return build || null;
    } catch (error) {
      throw new Error(`获取构建详情失败: ${error.message}`);
    }
  }

  // 获取用户的构建任务
  async getUserBuilds(userId, limit = 10) {
    try {
      // 获取队列中的任务
      const queueData = await fs.readJson(this.queueFile);
      const queueBuilds = queueData.queue.filter(build => build.userId === userId);
      
      // 获取历史记录中的任务
      const historyData = await fs.readJson(this.historyFile);
      const historyBuilds = historyData.history.filter(build => build.userId === userId);
      
      // 合并并按时间排序
      const allBuilds = [...queueBuilds, ...historyBuilds].sort((a, b) => {
        const timeA = new Date(a.queuedAt).getTime();
        const timeB = new Date(b.queuedAt).getTime();
        return timeB - timeA; // 降序排序
      });
      
      return allBuilds.slice(0, limit);
    } catch (error) {
      throw new Error(`获取用户构建任务失败: ${error.message}`);
    }
  }

  // 获取项目的构建任务
  async getProjectBuilds(projectId, limit = 10) {
    try {
      // 获取队列中的任务
      const queueData = await fs.readJson(this.queueFile);
      const queueBuilds = queueData.queue.filter(build => build.projectId === projectId);
      
      // 获取历史记录中的任务
      const historyData = await fs.readJson(this.historyFile);
      const historyBuilds = historyData.history.filter(build => build.projectId === projectId);
      
      // 合并并按时间排序
      const allBuilds = [...queueBuilds, ...historyBuilds].sort((a, b) => {
        const timeA = new Date(a.queuedAt).getTime();
        const timeB = new Date(b.queuedAt).getTime();
        return timeB - timeA; // 降序排序
      });
      
      return allBuilds.slice(0, limit);
    } catch (error) {
      throw new Error(`获取项目构建任务失败: ${error.message}`);
    }
  }

  // 取消构建任务
  async cancelBuild(buildId) {
    try {
      const queueData = await fs.readJson(this.queueFile);
      const buildIndex = queueData.queue.findIndex(build => build.id === buildId);
      
      if (buildIndex === -1) {
        throw new Error('构建任务不存在或已开始执行');
      }
      
      const build = queueData.queue[buildIndex];
      
      // 只能取消队列中的任务
      if (build.status !== 'queued') {
        throw new Error('只能取消等待中的构建任务');
      }
      
      // 更新状态为已取消
      build.status = 'cancelled';
      build.completedAt = new Date().toISOString();
      
      // 将取消的构建移到历史记录
      const historyData = await fs.readJson(this.historyFile);
      historyData.history.unshift(build);
      await fs.writeJson(this.historyFile, historyData, { spaces: 2 });
      
      // 从队列中移除
      queueData.queue.splice(buildIndex, 1);
      await fs.writeJson(this.queueFile, queueData, { spaces: 2 });
      
      return build;
    } catch (error) {
      throw new Error(`取消构建任务失败: ${error.message}`);
    }
  }

  // 检查是否可以开始新的构建
  async canStartNewBuild() {
    return this.activeBuilds.length < this.maxConcurrentBuilds;
  }

  // 将构建标记为活跃
  addActiveBuild(buildId) {
    this.activeBuilds.push(buildId);
  }

  // 将构建标记为完成
  removeActiveBuild(buildId) {
    const index = this.activeBuilds.indexOf(buildId);
    if (index !== -1) {
      this.activeBuilds.splice(index, 1);
    }
  }
}

module.exports = new BuildQueue(); 