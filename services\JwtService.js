const jwt = require('jsonwebtoken');
const fs = require('fs-extra');
const path = require('path');

class JwtService {
  constructor() {
    this.secretKey = process.env.JWT_SECRET || 'abc-1016-xyz';
    this.expiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.configDir = path.join(process.cwd(), 'data', 'tokens');
    fs.ensureDirSync(this.configDir);
  }

  /**
   * 生成JWT令牌
   * @param {Object} payload - 要编码到令牌中的数据
   * @returns {String} - JWT令牌
   */
  generateToken(payload) {
    return jwt.sign(payload, this.secretKey, { expiresIn: this.expiresIn });
  }

  /**
   * 验证JWT令牌
   * @param {String} token - 要验证的JWT令牌
   * @returns {Object|null} - 解码后的令牌数据或null（如果无效）
   */
  verifyToken(token) {
    try {
      return jwt.verify(token, this.secretKey);
    } catch (error) {
      return null;
    }
  }

  /**
   * 从请求头中提取令牌
   * @param {Object} req - Express请求对象
   * @returns {String|null} - 提取的令牌或null
   */
  extractTokenFromHeader(req) {
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
      return req.headers.authorization.substring(7);
    }
    return null;
  }

  /**
   * 将令牌添加到黑名单（用于注销）
   * @param {String} token - 要添加到黑名单的令牌
   */
  async addToBlacklist(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) return;
      
      const blacklistFile = path.join(this.configDir, 'token-blacklist.json');
      
      // 确保黑名单文件存在
      if (!(await fs.pathExists(blacklistFile))) {
        await fs.writeJson(blacklistFile, { tokens: [] }, { spaces: 2 });
      }
      
      const blacklist = await fs.readJson(blacklistFile);
      
      // 添加到黑名单
      blacklist.tokens.push({
        token,
        exp: decoded.exp,
        addedAt: new Date().toISOString()
      });
      
      // 清理过期的令牌
      const now = Math.floor(Date.now() / 1000);
      blacklist.tokens = blacklist.tokens.filter(item => item.exp > now);
      
      await fs.writeJson(blacklistFile, blacklist, { spaces: 2 });
    } catch (error) {
      console.error('添加令牌到黑名单失败:', error);
    }
  }

  /**
   * 检查令牌是否在黑名单中
   * @param {String} token - 要检查的令牌
   * @returns {Boolean} - 如果在黑名单中则为true
   */
  async isTokenBlacklisted(token) {
    try {
      const blacklistFile = path.join(this.configDir, 'token-blacklist.json');
      
      if (!(await fs.pathExists(blacklistFile))) {
        return false;
      }
      
      const blacklist = await fs.readJson(blacklistFile);
      return blacklist.tokens.some(item => item.token === token);
    } catch (error) {
      console.error('检查令牌黑名单失败:', error);
      return false;
    }
  }
}

module.exports = new JwtService();