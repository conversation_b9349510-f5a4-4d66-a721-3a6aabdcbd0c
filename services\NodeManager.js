// services/NodeManager.js
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const os = require('os');
const path = require('path');
const fs = require('fs-extra');

class NodeManager {
  constructor() {
    // 确定操作系统类型
    this.isWindows = os.platform() === 'win32';
    
    // 创建临时脚本目录
    this.tempDir = path.join(process.cwd(), 'temp', 'scripts');
    fs.ensureDirSync(this.tempDir);
  }

  // 在Windows上执行命令（支持NVS环境）
  async executeWindowsCommand(command, workingDir = null) {
    try {
      // 创建临时批处理文件
      const batchFilePath = path.join(this.tempDir, `command_${Date.now()}.bat`);

      // 确保temp/scripts目录存在
      await fs.ensureDir(this.tempDir);

      // 构建批处理文件内容
      let batchContent = '@echo off\r\n';
      if (workingDir) {
        batchContent += `cd /d "${workingDir}"\r\n`;
      }
      batchContent += `${command}\r\n`;

      // 写入批处理文件内容
      await fs.writeFile(batchFilePath, batchContent);

      // 执行批处理文件
      const result = await execPromise(`cmd.exe /c "${batchFilePath}"`, {
        cwd: workingDir || process.cwd(),
        windowsHide: true,
        maxBuffer: 1024 * 1024 * 10, // 10MB缓冲区
        timeout: 300000 // 5分钟超时
      });

      // 删除临时批处理文件
      await fs.remove(batchFilePath).catch(() => {});

      return result;
    } catch (error) {
      throw new Error(`执行Windows命令失败: ${error.message}`);
    }
  }

  // 获取已安装的Node.js版本
  async getInstalledVersions() {
    try {
      if (this.isWindows) {
        // 使用简化版的命令行测试NVS是否可用
        try {
          await execPromise('nvs --version');
          // 如果成功，说明NVS可用，获取已安装版本
          const { stdout: versionOutput } = await execPromise('nvs ls');
          
          // 解析版本
          let versions = [];
          const versionRegex = /v?(\d+\.\d+\.\d+)/g;
          let match;
          while ((match = versionRegex.exec(versionOutput)) !== null) {
            versions.push(match[1]);
          }
          
          return versions.length > 0 ? versions : ['14.21.3', '16.20.2', '18.20.3'];
        } catch (error) {
          // NVS不可用，返回系统Node版本
          const nodeVersionResult = await execPromise('node -v');
          const version = nodeVersionResult.stdout.trim().replace('v', '');
          return [version];
        }
      } else {
        // Unix系统
        try {
          const { stdout } = await execPromise('nvs ls');
          
          // 解析版本
          let versions = [];
          const versionRegex = /v?(\d+\.\d+\.\d+)/g;
          let match;
          while ((match = versionRegex.exec(stdout)) !== null) {
            versions.push(match[1]);
          }
          
          return [...new Set(versions)]; // 去除重复
        } catch (error) {
          // 如果NVS不可用，尝试获取系统Node版本
          const nodeVersionResult = await execPromise('node -v');
          const version = nodeVersionResult.stdout.trim().replace('v', '');
          return [version];
        }
      }
    } catch (error) {
      // 如果出错，返回空数组
      console.error('获取已安装的Node.js版本失败:', error.message);
      return [];
    }
  }

  // 切换到指定的Node.js版本
  async switchVersion(version) {
    try {
      if (!version) {
        throw new Error('未指定Node.js版本');
      }
      
      // 尝试直接使用node命令检查当前版本
      try {
        const { stdout } = await execPromise('node -v');
        const currentVersion = stdout.trim().replace('v', '');
        if (currentVersion === version) {
          return { success: true, message: `当前已经是Node.js v${version}` };
        }
      } catch (error) {
        // 忽略错误，继续尝试切换版本
      }
      
      if (this.isWindows) {
        try {
          // 尝试使用NVS切换版本
          const result = await execPromise(`nvs use ${version}`);
          return { success: true, message: result.stdout };
        } catch (error) {
          // 如果失败，尝试安装该版本
          await this.installVersion(version);
          // 再次尝试切换
          const result = await execPromise(`nvs use ${version}`);
          return { success: true, message: result.stdout };
        }
      } else {
        // Unix系统
        try {
          const result = await execPromise(`nvs use ${version}`);
          return { success: true, message: result.stdout };
        } catch (error) {
          // 如果失败，尝试安装该版本
          await this.installVersion(version);
          // 再次尝试切换
          const result = await execPromise(`nvs use ${version}`);
          return { success: true, message: result.stdout };
        }
      }
    } catch (error) {
      throw new Error(`切换Node.js版本失败: ${error.message}`);
    }
  }

  // 安装指定的Node.js版本
  async installVersion(version) {
    try {
      if (this.isWindows) {
        // Windows系统
        const result = await execPromise(`nvs add ${version}`);
        return { success: true, message: result.stdout };
      } else {
        // Unix系统
        const result = await execPromise(`nvs add ${version}`);
        return { success: true, message: result.stdout };
      }
    } catch (error) {
      throw new Error(`安装Node.js版本失败: ${error.message}`);
    }
  }

  // 安装依赖（带有legacy peer deps选项）
  async installDependencies(projectDir, useNodeVersion = null,packageManager = 'npm') {

    try {
      // 如果指定了Node.js版本
      if (useNodeVersion) {
        try {
          // 尝试切换到指定版本
          await this.switchVersion(useNodeVersion);
        } catch (error) {
          console.warn(`切换Node.js版本失败，将使用当前版本: ${error.message}`);
        }
      }
      
      // 使用legacy peer deps默认选项避免常见的依赖冲突
      let command = 'yarn install --legacy-peer-deps';
      if(packageManager == 'npm'){
        command = 'npm install --legacy-peer-deps';
      }
      let stdout, stderr;
      
      if (this.isWindows && useNodeVersion) {
        // Windows系统且指定了Node.js版本，使用改进的命令执行方法
        const fullCommand = `call nvs use ${useNodeVersion} && ${command}`;

        try {
          const result = await this.executeWindowsCommand(fullCommand, projectDir);
          stdout = result.stdout;
          stderr = result.stderr;
        } catch (error) {
          // 如果NVS切换失败，尝试直接执行npm install
          console.warn(`NVS切换版本失败，使用当前Node.js版本: ${error.message}`);
          const result = await execPromise(command, { cwd: projectDir });
          stdout = result.stdout;
          stderr = result.stderr;
        }
      } else {
        // 非Windows系统或Windows但未指定Node.js版本
        const fullCommand = `nvs use ${useNodeVersion} && ${command}`;
        const result = await execPromise(fullCommand, { cwd: projectDir });
        stdout = result.stdout;
        stderr = result.stderr;
      }
      
      return { success: true, stdout, stderr };
    } catch (error) {
      throw new Error(`安装依赖失败: ${error.message}`);
    }
  }
}

module.exports = new NodeManager(); 