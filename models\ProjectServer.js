const fs = require('fs-extra');
const path = require('path');

class ProjectServer {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'configs');
    this.relationshipsFile = path.join(this.configDir, 'project_servers.json');
    fs.ensureDirSync(this.configDir);
    this.initRelationshipsFile();
  }

  // 初始化项目-服务器关系配置文件
  async initRelationshipsFile() {
    try {
      if (!(await fs.pathExists(this.relationshipsFile))) {
        await fs.writeJson(this.relationshipsFile, { relationships: [] }, { spaces: 2 });
      }
    } catch (error) {
      throw new Error(`初始化项目-服务器关系配置文件失败: ${error.message}`);
    }
  }

  // 获取项目的所有服务器关系
  async getByProjectId(projectId) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      return data.relationships.filter(rel => rel.projectId === projectId);
    } catch (error) {
      throw new Error(`获取项目服务器关系失败: ${error.message}`);
    }
  }

  // 获取服务器的所有项目关系
  async getByServerId(serverId) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      return data.relationships.filter(rel => rel.serverId === serverId);
    } catch (error) {
      throw new Error(`获取服务器项目关系失败: ${error.message}`);
    }
  }

  // 添加项目-服务器关系
  async addRelationship(projectId, serverId, deployPath) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      
      // 检查是否已存在相同关系
      const existingIndex = data.relationships.findIndex(
        rel => rel.projectId === projectId && rel.serverId === serverId
      );
      
      if (existingIndex !== -1) {
        // 更新部署路径
        data.relationships[existingIndex].deployPath = deployPath;
      } else {
        // 添加新关系
        data.relationships.push({
          projectId,
          serverId,
          deployPath,
          createdAt: new Date().toISOString()
        });
      }
      
      await fs.writeJson(this.relationshipsFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`添加项目-服务器关系失败: ${error.message}`);
    }
  }

  // 批量设置项目的服务器关系
  async setProjectServers(projectId, serverConfigs) {
    try {
      // 首先删除该项目的所有现有关系
      await this.removeAllProjectRelationships(projectId);
      
      // 添加新的关系
      const data = await fs.readJson(this.relationshipsFile);
      
      for (const config of serverConfigs) {
        data.relationships.push({
          projectId,
          serverId: config.serverId,
          deployPath: config.deployPath,
          createdAt: new Date().toISOString()
        });
      }
      
      await fs.writeJson(this.relationshipsFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`设置项目服务器关系失败: ${error.message}`);
    }
  }

  // 删除项目-服务器关系
  async removeRelationship(projectId, serverId) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      
      const initialLength = data.relationships.length;
      data.relationships = data.relationships.filter(
        rel => !(rel.projectId === projectId && rel.serverId === serverId)
      );
      
      if (data.relationships.length === initialLength) {
        return false; // 没有找到关系
      }
      
      await fs.writeJson(this.relationshipsFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`删除项目-服务器关系失败: ${error.message}`);
    }
  }

  // 删除项目的所有服务器关系
  async removeAllProjectRelationships(projectId) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      
      data.relationships = data.relationships.filter(
        rel => rel.projectId !== projectId
      );
      
      await fs.writeJson(this.relationshipsFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`删除项目所有服务器关系失败: ${error.message}`);
    }
  }

  // 删除服务器的所有项目关系
  async removeAllServerRelationships(serverId) {
    try {
      const data = await fs.readJson(this.relationshipsFile);
      
      data.relationships = data.relationships.filter(
        rel => rel.serverId !== serverId
      );
      
      await fs.writeJson(this.relationshipsFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`删除服务器所有项目关系失败: ${error.message}`);
    }
  }
}

module.exports = new ProjectServer(); 