<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户管理 - 前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/animate.min.css">
  <style>
    :root {
      --primary-color: #4e73df;
      --secondary-color: #6f42c1;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
      --warning-color: #f6c23e;
      --danger-color: #e74a3b;
      --light-color: #f8f9fc;
      --dark-color: #5a5c69;
      --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --transition-speed: 0.3s;
      --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --box-shadow-hover: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    body {
      background-color: #f8f9fc;
      font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    /* 滚动条美化 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    .sidebar {
      min-height: 100vh;
      background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      box-shadow: var(--card-shadow);
      position: sticky;
      top: 0;
      transition: all var(--transition-speed) ease-in-out;
      z-index: 100;
    }
    
    .nav-link {
      padding: 1rem 1.5rem;
      margin-bottom: 0.2rem;
      border-radius: 0.35rem;
      transition: all var(--transition-speed) ease-in-out;
      display: flex;
      align-items: center;
    }
    
    .nav-link:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }
    
    .nav-link.active {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 0.35rem;
      font-weight: 600;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .nav-link i {
      margin-right: 0.75rem;
      font-size: 1.1rem;
    }
    
    .content-area {
      padding: 1.5rem;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .user-card {
      border: none;
      border-radius: 0.5rem;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      background-color: #fff;
      box-shadow: var(--card-shadow);
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
      position: relative;
      overflow: hidden;
    }
    
    .user-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    .user-card.processing {
      pointer-events: none;
      opacity: 0.7;
    }
    
    .user-avatar {
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .user-card:hover .user-avatar {
      transform: scale(1.1);
    }
    
    .user-info {
      background-color: rgba(248, 249, 252, 0.5);
      border-radius: 0.5rem;
      padding: 0.75rem;
    }
    
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 10rem;
      font-weight: 600;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .user-card:hover .status-badge {
      transform: scale(1.05);
    }
    
    .stats-card {
      background: linear-gradient(135deg, #4e73df 0%, #6f42c1 100%);
      color: white;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }
    
    .stats-item {
      padding: 1rem;
      border-radius: 0.5rem;
      background-color: rgba(255, 255, 255, 0.1);
      transition: transform var(--transition-speed);
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
    }
    
    .stats-item:hover {
      transform: translateY(-5px);
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    .stats-item .icon-wrapper {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      color: white;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .stats-item:hover .icon-wrapper {
      transform: rotate(15deg);
    }
    
    .stats-item .icon-wrapper i {
      font-size: 1.25rem;
    }
    
    .stats-item .stats-info {
      flex: 1;
    }
    
    .stats-item .stats-info .stats-label {
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.25rem;
    }
    
    .stats-item .stats-info .stats-value {
      font-size: 1.25rem;
      font-weight: 600;
      color: white;
    }
    
    .btn {
      border-radius: 0.35rem;
      padding: 0.375rem 0.75rem;
      font-weight: 600;
      transition: all var(--transition-speed);
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn i {
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .btn:hover i {
      transform: scale(1.2);
    }
    
    .btn-group .btn {
      border-radius: 0.35rem;
      margin-right: 0.25rem;
    }
    
    .nav-tabs {
      border-bottom: 1px solid #dee2e6;
      margin-bottom: 0;
    }
    
    .nav-tabs .nav-link {
      border-radius: 0.35rem 0.35rem 0 0;
      font-weight: 600;
      padding: 0.75rem 1rem;
      transition: all var(--transition-speed) ease-in-out;
      color: var(--dark-color);
      border: 1px solid transparent;
      margin-bottom: -1px; /* 确保激活状态下边框覆盖tab-content上边框 */
    }
    
    .nav-tabs .nav-link:hover {
      background-color: rgba(78, 115, 223, 0.05);
      border-color: #e9ecef #e9ecef #dee2e6;
    }
    
    .nav-tabs .nav-link.active {
      color: var(--primary-color);
      background-color: #fff;
      border-color: #dee2e6 #dee2e6 #fff;
      font-weight: 700;
      position: relative;
      z-index: 2;
      box-shadow: 0 -3px 8px rgba(0, 0, 0, 0.05);
    }
    
    .nav-tabs .nav-link.active:after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #fff;
    }
    
    .nav-tabs .nav-link.active i {
      color: var(--primary-color);
    }
    
    .nav-tabs .nav-link i {
      margin-right: 0.5rem;
    }
    
    .tab-content {
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-top: none;
      border-radius: 0 0 0.35rem 0.35rem;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .tab-pane {
      transition: opacity 0.3s ease-in-out;
    }
    
    .tab-pane.fade {
      opacity: 0;
    }
    
    .tab-pane.fade.show {
      opacity: 1;
    }
    
    .modal-content {
      border: none;
      border-radius: 0.5rem;
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }
    
    .modal-header {
      border-radius: 0.5rem 0.5rem 0 0;
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      color: white;
    }
    
    .modal-header .modal-title {
      font-weight: 700;
      display: flex;
      align-items: center;
    }
    
    .modal-header .modal-title i {
      margin-right: 0.5rem;
    }
    
    .modal-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .table {
      border-radius: 0.35rem;
      overflow: hidden;
    }
    
    .page-header {
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e3e6f0;
    }
    
    /* 动画类 */
    .animate__animated {
      animation-duration: 0.5s;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .action-buttons {
        width: 100%;
        margin-top: 1rem;
      }
      
      .user-card .d-flex {
        flex-direction: column;
      }
      
      .user-info {
        margin-top: 1rem;
      }
      
      .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
      }
      
      .tab-content {
        padding: 1rem !important;
      }
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 主内容区域 -->
      <main class="col-md-12 px-md-4 animate__animated animate__fadeIn">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4 page-header">
          <div>
            <h1 class="h2 mb-0"><i class="bi bi-people-fill me-2 text-primary"></i>用户管理</h1>
          </div>
          <div class="btn-toolbar">
            <button class="btn btn-outline-primary me-2" id="refresh-btn">
              <i class="bi bi-arrow-clockwise me-1"></i> 刷新数据
            </button>
            <button class="btn btn-primary" id="create-admin-btn">
              <i class="bi bi-person-plus-fill me-1"></i> 创建管理员
            </button>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="stats-card animate__animated animate__fadeInUp">
              <h5 class="mb-3"><i class="bi bi-graph-up me-2"></i>用户统计概览</h5>
              <div class="row g-3" id="user-stats">
                <!-- 统计数据将在这里动态加载 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 内容卡片 -->
        <div class="card shadow-sm border-0 animate__animated animate__fadeInUp animate__delay-1s">
          <div class="card-body p-0">
            <!-- 标签页 -->
            <ul class="nav nav-tabs px-3 pt-3" id="userTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                  <i class="bi bi-hourglass-split me-1"></i> 待审核用户 
                  <span class="badge bg-warning ms-1 text-dark fw-bold" id="pending-count">0</span>
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                  <i class="bi bi-people-fill me-1"></i> 所有用户
                </button>
              </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content p-4 p-md-5" id="userTabContent">
              <!-- 待审核用户 -->
              <div class="tab-pane fade show active animate__animated animate__fadeIn" id="pending" role="tabpanel">
                <div id="pending-users-container">
                  <!-- 待审核用户列表将在这里动态加载 -->
                  <div class="text-center py-5 placeholder-glow">
                    <div class="spinner-border text-primary mb-3" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载用户数据...</p>
                  </div>
                </div>
              </div>

              <!-- 所有用户 -->
              <div class="tab-pane fade animate__animated animate__fadeIn" id="all" role="tabpanel">
                <div id="all-users-container">
                  <!-- 所有用户列表将在这里动态加载 -->
                  <div class="text-center py-5 placeholder-glow">
                    <div class="spinner-border text-primary mb-3" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载用户数据...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 创建管理员模态框 -->
  <div class="modal fade" id="createAdminModal" tabindex="-1" aria-labelledby="createAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createAdminModalLabel"><i class="bi bi-shield-plus me-2"></i>创建管理员</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4">
          <form id="create-admin-form">
            <div class="mb-4">
              <label for="admin-username" class="form-label"><i class="bi bi-person me-1"></i>用户名</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                <input type="text" class="form-control" id="admin-username" placeholder="输入管理员用户名" required>
              </div>
              <small class="text-muted">用户名将用于登录系统，不可更改</small>
            </div>
            <div class="mb-4">
              <label for="admin-password" class="form-label"><i class="bi bi-key me-1"></i>密码</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                <input type="password" class="form-control" id="admin-password" placeholder="输入安全密码" required>
              </div>
              <small class="text-muted">请使用强密码以确保账户安全</small>
            </div>
            <div class="mb-4">
              <label for="admin-email" class="form-label"><i class="bi bi-envelope me-1"></i>邮箱</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                <input type="email" class="form-control" id="admin-email" placeholder="<EMAIL>">
              </div>
              <small class="text-muted">用于接收系统通知和密码重置</small>
            </div>
            <div class="mb-4">
              <label for="admin-displayName" class="form-label"><i class="bi bi-card-heading me-1"></i>显示名称</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-person-vcard"></i></span>
                <input type="text" class="form-control" id="admin-displayName" placeholder="输入显示名称">
              </div>
              <small class="text-muted">在界面上显示的名称，可以使用中文</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" id="save-admin-btn">
            <i class="bi bi-person-plus-fill me-1"></i>创建管理员
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户详情模态框 -->
  <div class="modal fade" id="userDetailModal" tabindex="-1" aria-labelledby="userDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered animate__animated animate__fadeInDown">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userDetailModalLabel"><i class="bi bi-person-badge me-2"></i>用户详情</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-4" id="user-detail-content">
          <!-- 用户详情将在这里动态加载 -->
          <div class="text-center py-5 placeholder-glow">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted">正在加载用户详情...</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 操作成功提示框 -->
  <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="bi bi-check-circle me-2"></i><span id="successToastMessage">操作成功</span>
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/user-management.js"></script>
</body>
</html>
