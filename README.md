# 前端项目自动化构建部署工具

这是一个基于 Node.js 的前端项目自动化构建部署工具，提供 Web 界面，支持多项目、多人员同时构建和部署。

## 功能特性

- 可视化 Web 界面，非研发人员也能轻松使用
- 支持从 Git 拉取项目代码
- 支持选择分支进行构建
- 自动安装依赖、执行构建
- 支持将构建产物部署到多个服务器
- 实时显示部署日志
- 支持多项目、多人员同时构建
- 不同项目可以部署到不同的服务器
- 支持为每个项目指定不同的 Node.js 版本（自动使用 nvs 切换）
- 自动处理依赖冲突问题（使用 --legacy-peer-deps）

## 技术栈

- 后端：Node.js、Express、Socket.IO
- 前端：Bootstrap 5、EJS 模板引擎
- 工具：simple-git、node-ssh、archiver、nvs（Node 版本管理）

## 安装与运行

### 环境要求

- Node.js 14.x 或更高版本
- npm 6.x 或更高版本
- nvs（Node Version Switcher）

### 安装步骤

1. 克隆项目代码

```bash
git clone <项目仓库地址>
cd deploy-dev
```

2. 安装依赖

```bash
npm install
```

3. 配置环境变量

复制 `.env.example` 文件为 `.env`，并根据需要修改配置：

```bash
cp .env.example .env
```

4. 启动服务

```bash
npm start
```

启动后，访问 `http://localhost:3000` 即可打开 Web 界面。

## 使用指南

### 添加服务器

1. 点击"服务器"导航菜单
2. 点击"添加服务器"按钮
3. 填写服务器信息（名称、主机、端口、用户名、密码、部署路径等）
4. 点击"保存服务器"按钮

### 添加项目

1. 点击"项目"导航菜单
2. 点击"添加项目"按钮
3. 填写项目信息（名称、Git URL、构建命令、输出目录等）
4. 选择 Node.js 版本（可选，不选则使用系统默认版本）
5. 选择部署服务器
6. 点击"保存项目"按钮

### 部署项目

#### 方式一：快速部署

1. 在仪表盘页面，从"快速部署"区域选择项目
2. 选择要部署的分支
3. 点击"部署"按钮
4. 查看部署进度和日志

#### 方式二：从项目列表部署

1. 点击"项目"导航菜单
2. 在项目列表中找到要部署的项目
3. 点击"部署"按钮
4. 选择要部署的分支
5. 点击"开始部署"按钮
6. 查看部署进度和日志

## 目录结构

```
deploy-dev/
├── data/               # 数据存储目录
│   ├── builds/         # 构建产物目录
│   ├── configs/        # 配置文件目录
│   └── projects/       # 项目代码目录
├── logs/               # 日志目录
├── models/             # 数据模型
├── public/             # 静态资源
│   ├── css/            # 样式文件
│   └── js/             # JavaScript 文件
├── routes/             # 路由处理
├── services/           # 业务逻辑服务
│   ├── DeployService.js  # 部署服务
│   ├── NodeManager.js    # Node.js 版本管理服务
│   └── ...               # 其他服务
├── views/              # 视图模板
├── .env                # 环境变量配置
├── main.js             # 应用入口
└── package.json        # 项目配置
```

## 多 Node.js 版本支持

本系统支持为每个项目指定特定的 Node.js 版本，主要功能包括：

1. 使用 nvs 自动切换项目所需的 Node.js 版本
2. 在项目配置中可以选择 Node.js 版本
3. 自动处理依赖安装问题，使用 --legacy-peer-deps 解决依赖冲突
4. 提供 API 接口获取和管理 Node.js 版本

要使用此功能，请确保系统已安装 nvs (Node Version Switcher)。

## 注意事项

- 生产环境中建议使用 SSH 密钥而非密码进行服务器认证
- 定期清理 `data/builds` 目录，避免占用过多磁盘空间
- 可以通过修改 `.env` 文件调整服务端口和日志级别
- 使用多 Node.js 版本功能时，请确保系统已正确安装 nvs 