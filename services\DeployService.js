const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { NodeSSH } = require('node-ssh');
const simpleGit = require('simple-git');
const archiver = require('archiver');
const extract = require('extract-zip');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const os = require('os'); // 添加os模块

const Project = require('../models/Project');
const Server = require('../models/Server');
const NodeManager = require('./NodeManager');
const VcsService = require('./VcsService');
const SecurityAgent = require('./SecurityAgent');
const DeploymentHistory = require('../models/DeploymentHistory');
class DeployService {
  constructor() {
    this.projectsDir = path.join(process.cwd(), 'data', 'projects');
    this.buildsDir = path.join(process.cwd(), 'data', 'builds');
    this.tempDir = path.join(process.cwd(), 'temp', 'scripts');
    fs.ensureDirSync(this.projectsDir);
    fs.ensureDirSync(this.buildsDir);
    fs.ensureDirSync(this.tempDir);
  }

  // 在Windows上执行命令（支持NVS环境）
  async executeWindowsCommand(command, workingDir = null) {
    try {
      // 创建临时批处理文件
      const batchFilePath = path.join(this.tempDir, `command_${Date.now()}.bat`);

      // 确保temp/scripts目录存在
      await fs.ensureDir(this.tempDir);

      // 构建批处理文件内容
      let batchContent = '@echo off\r\n';
      if (workingDir) {
        batchContent += `cd /d "${workingDir}"\r\n`;
      }
      batchContent += `${command}\r\n`;

      // 写入批处理文件内容
      await fs.writeFile(batchFilePath, batchContent);

      // 执行批处理文件
      const result = await execPromise(`cmd.exe /c "${batchFilePath}"`, {
        cwd: workingDir || process.cwd(),
        windowsHide: true,
        maxBuffer: 1024 * 1024 * 10, // 10MB缓冲区
        timeout: 300000 // 5分钟超时
      });

      // 删除临时批处理文件
      await fs.remove(batchFilePath).catch(() => {});

      return result;
    } catch (error) {
      throw new Error(`执行Windows命令失败: ${error.message}`);
    }
  }

  // 获取项目可用分支
  async getBranches(projectId) {
    try {
      const project = await Project.getById(projectId);
      if (!project) {
        throw new Error('项目不存在');
      }

      const projectDir = path.join(this.projectsDir, projectId);
      
      // VcsService已经是实例化的对象，不需要再次实例化
      // const vcsService = new VcsService();
      
      // 克隆或更新仓库
      if (!(await fs.pathExists(projectDir))) {
        await this.cloneRepository(project.gitUrl, projectDir, project.vcsType, project.vcsCredentialId);
      } else {
        await this.fetchRepository(project.gitUrl, projectDir, project.vcsType, project.vcsCredentialId);
      }

      // 获取分支
      if (project.vcsType === 'git') {
        const git = simpleGit(projectDir);
        // 先获取最新的远程仓库信息
        await git.fetch();
        const branches = await git.branch(['-r']);
        
        // 格式化分支名称
        return branches.all.map(branch => branch.replace('origin/', ''))
          .filter(branch => branch !== 'HEAD -> origin/main');
      } else {
        // 对于SVN，返回空数组或实现SVN分支获取逻辑
        return ['svn'];
      }
    } catch (error) {
      throw new Error(`获取分支失败: ${error.message}`);
    }
  }

  // 克隆仓库（支持Git和SVN）
  async cloneRepository(repositoryUrl, targetDir, vcsType = 'git', credentialId = null, branch = 'main') {
    try {
      const result = await VcsService.cloneRepository(repositoryUrl, targetDir, vcsType, credentialId, branch);
      return result.success;
    } catch (error) {
      throw new Error(`克隆仓库失败: ${error.message}`);
    }
  }

  // 拉取仓库最新变更（支持Git和SVN）
  async fetchRepository(repositoryUrl,repoDir, vcsType = 'git', credentialId = null) {
    try {
      const result = await VcsService.updateRepository(repositoryUrl,repoDir, vcsType, credentialId);
      return result.success;
    } catch (error) {
      throw new Error(`拉取仓库失败: ${error.message}`);
    }
  }

  // 检出指定分支（Git专用）
  async checkoutBranch(projectDir, branch, credentialId = null) {
    try {
      const result = await VcsService.checkoutBranch(projectDir, branch, credentialId);
      return result.success;
    } catch (error) {
      throw new Error(`检出分支失败: ${error.message}`);
    }
  }

  // 安装依赖
  async installDependencies(projectDir, nodeVersion,packageManager = 'npm') {
    try {
      // 使用NodeManager安装依赖，自动处理Node.js版本切换
      return await NodeManager.installDependencies(projectDir, nodeVersion, packageManager);
    } catch (error) {
      throw new Error(`安装依赖失败: ${error.message}`);
    }
  }

  // 构建项目
  async buildProject(projectDir, buildCommand, nodeVersion) {
    try {
      // 再次验证构建命令（双重验证）
      if (!SecurityAgent.validateBuildCommand(buildCommand)) {
        throw new Error('构建命令不安全，已被拒绝执行');
      }
      // 如果指定了Node.js版本，先切换
      if (nodeVersion) {
        await NodeManager.switchVersion(nodeVersion);
      }

      let stdout, stderr;

      // 检查是否为Windows系统并且指定了Node.js版本
      if (os.platform() === 'win32' && nodeVersion) {
        // Windows系统且指定了Node.js版本，使用改进的命令执行方法
        const fullCommand = `call nvs use ${nodeVersion} && ${buildCommand}`;

        try {
          const result = await this.executeWindowsCommand(fullCommand, projectDir);
          stdout = result.stdout;
          stderr = result.stderr;
        } catch (error) {
          // 如果NVS切换失败，尝试直接执行构建命令
          console.warn(`NVS切换版本失败，使用当前Node.js版本: ${error.message}`);
          const result = await execPromise(buildCommand, { cwd: projectDir });
          stdout = result.stdout;
          stderr = result.stderr;
        }
      } else {
        const fullCommand = `nvs use ${nodeVersion} && ${buildCommand}`;
        // 非Windows系统或未指定Node.js版本
        const result = await execPromise(fullCommand, {
          cwd: projectDir,
          maxBuffer: 1024 * 1024 * 10, // 10MB缓冲区
          timeout: 300000 // 5分钟超时 
        });
        stdout = result.stdout;
        stderr = result.stderr;
      }

      return { success: true, stdout, stderr };
    } catch (error) {
      throw new Error(`构建项目失败: ${error.message}`);
    }
  }

  // 创建构建产物的压缩包
  async createArchive(sourceDir, outputPath) {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', {
        zlib: { level: 9 }
      });

      output.on('close', () => {
        resolve({
          success: true,
          size: archive.pointer(),
          path: outputPath
        });
      });

      archive.on('error', (err) => {
        reject(new Error(`创建压缩包失败: ${err.message}`));
      });

      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }

  // 解压压缩包
  async extractArchive(archivePath, outputDir) {
    try {
      await extract(archivePath, { dir: outputDir });
      return { success: true };
    } catch (error) {
      throw new Error(`解压失败: ${error.message}`);
    }
  }

  // 清理服务器上的旧备份文件（保留最近N个备份）
  async cleanupOldBackups(server, remotePath, keepCount = 10) {
    const ssh = new NodeSSH();

    try {
      // 连接服务器
      await ssh.connect({
        host: server.host,
        port: server.port || 22,
        username: server.username,
        password: server.password
      });

      const backupDir = `${remotePath}/backups`;

      // 获取备份目录中的所有zip文件，按时间排序
      const listResult = await ssh.execCommand(`ls -t ${backupDir}/*.zip 2>/dev/null || echo "no_backups"`);

      if (listResult.stdout && listResult.stdout !== 'no_backups') {
        const backupFiles = listResult.stdout.trim().split('\n');

        // 如果备份文件数量超过保留数量，删除多余的
        if (backupFiles.length > keepCount) {
          const filesToDelete = backupFiles.slice(keepCount);
          for (const file of filesToDelete) {
            await ssh.execCommand(`rm -f "${file}"`);
          }

          ssh.dispose();
          return {
            success: true,
            deletedCount: filesToDelete.length,
            remainingCount: keepCount
          };
        }
      }

      ssh.dispose();
      return { success: true, deletedCount: 0, remainingCount: backupFiles?.length || 0 };
    } catch (error) {
      ssh.dispose();
      throw new Error(`清理备份文件失败: ${error.message}`);
    }
  }

  // 使用SSH部署到服务器
  async deployToServer(server, archivePath, remotePath) {
    const ssh = new NodeSSH();

    try {
      // 连接服务器
      await ssh.connect({
        host: server.host,
        port: server.port || 22,
        username: server.username,
        password: server.password
      });

      // 如果远程目录不存在则创建
      await ssh.execCommand(`mkdir -p ${remotePath}`);

      // 创建备份目录
      const backupDir = `${remotePath}`;
      await ssh.execCommand(`mkdir -p ${backupDir}`);

      // 获取压缩包的文件名（包含日期）
      const archiveFileName = path.basename(archivePath);
      const remoteArchivePath = `${backupDir}/${archiveFileName}`;

      // 上传压缩包到备份目录
      await ssh.putFile(archivePath, remoteArchivePath);

      // 在远程服务器解压到部署目录
      await ssh.execCommand(`unzip -o ${remoteArchivePath} -d ${remotePath}`);

      // 不删除备份文件，保留在服务器上
      // 注释掉原来的清理代码：await ssh.execCommand(`rm ${remoteArchivePath}`);

      // 断开连接
      ssh.dispose();

      return {
        success: true,
        backupPath: remoteArchivePath,
        archiveFileName: archiveFileName
      };
    } catch (error) {
      ssh.dispose();
      throw new Error(`部署到服务器 ${server.name} 失败: ${error.message}`);
    }
  }

  // 从历史备份还原部署
  async restoreDeployment(projectId, serverId, historyId, socketIo) {
    try {
      const project = await Project.getById(projectId);
      if (!project) {
        throw new Error('项目不存在');
      }

      const server = await Server.getById(serverId);
      if (!server) {
        throw new Error('服务器不存在');
      }

      // 获取部署历史
      const history = await DeploymentHistory.getDeploymentHistory(projectId, serverId);
      const record = history.find(h => h.id === historyId);

      if (!record) {
        throw new Error('未找到指定的部署历史记录');
      }

      // 构建日志函数
      const buildId = uuidv4();
      const log = (message, level = 'info') => {
        const logMessage = {
          time: new Date().toISOString(),
          message,
          buildId,
          level
        };

        socketIo.emit(`build-${buildId}`, logMessage);
        socketIo.emit('build-log', {
          ...logMessage,
          projectId,
          projectName: project.name,
          branch: record.branch
        });

        return logMessage;
      };

      log(`开始还原项目 ${project.name} 到服务器 ${server.name} 的历史版本: ${record.archiveFileName}`);

      // 连接服务器
      const ssh = new NodeSSH();
      await ssh.connect({
        host: server.host,
        port: server.port || 22,
        username: server.username,
        password: server.password
      });

      // 构建备份文件路径
      const remoteArchivePath = `${server.deployPath}/${record.archiveFileName}`;

      // 检查备份文件是否存在
      const checkResult = await ssh.execCommand(`ls ${remoteArchivePath} 2>/dev/null || echo "not_found"`);
      if (checkResult.stdout.trim() === 'not_found') {
        ssh.dispose();
        throw new Error(`服务器上未找到备份文件: ${record.archiveFileName}`);
      }

      // 解压备份文件到部署目录
      log(`开始解压备份文件: ${record.archiveFileName}`);
      await ssh.execCommand(`unzip -o ${remoteArchivePath} -d ${server.deployPath}`);
      log(`还原完成，已成功解压 ${record.archiveFileName} 到 ${server.deployPath}`);

      // 断开连接
      ssh.dispose();

      // 记录这次还原操作
      await DeploymentHistory.recordDeployment(
        projectId,
        serverId,
        record.archiveFileName,
        record.branch,
        new Date()
      );
      log(`已记录还原操作: ${record.archiveFileName}`);

      return {
        success: true,
        message: `成功还原到版本 ${record.archiveFileName}`,
        buildId
      };
    } catch (error) {
      throw new Error(`还原部署失败: ${error.message}`);
    }
  }

  // 完整部署流程
  async deploy(projectId, branch, socketIo, buildId = null, specificServers = null) {
    try {
      buildId = buildId || uuidv4();
      const project = await Project.getById(projectId);
      
      if (!project) {
        throw new Error('项目不存在');
      }

      
      let servers = [];
      // 获取服务器列表
      if (specificServers && specificServers.length > 0) {
        servers = specificServers.map(server => server.serverId ? server.serverId : server);
      }
      let projectServers = await Server.getServersByProjectId(projectId,servers);
      
      
      if (projectServers.length === 0) {
        throw new Error('该项目没有配置服务器');
      }
      
      const projectDir = path.join(this.projectsDir, projectId);
      const buildDir = path.join(this.buildsDir, buildId);
      fs.ensureDirSync(buildDir);
      
      // 日志函数，用于向客户端发送更新
      const log = (message, level = 'info') => {
        const logMessage = {
          time: new Date().toISOString(),
          message,
          buildId,
          level // 添加日志级别：info, warn, error
        };

        // 发送到特定构建的频道
        socketIo.emit(`build-${buildId}`, logMessage);

        // 发送到全局构建频道
        socketIo.emit('build-log', {
          ...logMessage,
          projectId,
          projectName: project.name,
          branch
        });

        return logMessage;
      };

      // 错误日志函数
      const logError = (message, error = null) => {
        const errorMessage = error ? `${message}: ${error.message}` : message;
        log(errorMessage, 'error');

        // 发送错误事件
        socketIo.emit(`build-error-${buildId}`, {
          time: new Date().toISOString(),
          message: errorMessage,
          buildId,
          projectId,
          projectName: project.name,
          branch,
          error: error ? {
            message: error.message,
            stack: error.stack
          } : null
        });

        return errorMessage;
      };
      
      // 开始部署流程
      log(`开始部署 ${project.name} (${branch}) 到 ${projectServers.length} 个服务器`);

      try {
        // 获取仓库配置
        const repositoryUrl = project.repositoryUrl || project.gitUrl; // 兼容旧字段
        const vcsType = project.vcsType || 'git';
        const credentialId = project.vcsCredentialId || null;

        // 克隆或更新仓库
        if (!(await fs.pathExists(projectDir))) {
          log(`克隆${vcsType.toUpperCase()}仓库 ${repositoryUrl}`);
          await this.cloneRepository(repositoryUrl, projectDir, vcsType, credentialId, branch);
        } else {
          log('拉取最新变更');
          await this.fetchRepository(repositoryUrl,projectDir, vcsType, credentialId);
        }

        // 检出分支（仅Git支持）
        if (vcsType === 'git') {
          log(`检出分支 ${branch}`);
          await this.checkoutBranch(projectDir, branch, credentialId);
        } else {
          log(`SVN仓库不支持分支切换，使用默认分支`);
        }

        // 如果项目指定了Node.js版本，先切换
        if (project.nodeVersion) {
          log(`切换到项目指定的Node.js版本: ${project.nodeVersion}`);
          try {
            await NodeManager.switchVersion(project.nodeVersion);
          } catch (error) {
            logError('Node.js版本切换失败，将使用当前版本继续构建', error);
          }
        }

        // 安装依赖
        log('安装依赖');
        const installResult = await this.installDependencies(projectDir, project.nodeVersion, project.packageManager);

        log(`依赖安装完成: ${installResult.stdout}`);

        // 构建项目
        log(`构建项目，执行命令: ${project.buildCommand}`);
        const buildResult = await this.buildProject(projectDir, project.buildCommand, project.nodeVersion);
        log(`构建完成: ${buildResult.stdout}`);
      } catch (error) {
        logError('构建过程中发生错误', error);
        throw error; // 重新抛出错误，让外层catch处理
      }
      
      // 创建压缩包（使用日期命名）
      const outputDir = path.join(projectDir, project.outputDir);

      // 生成日期格式的文件名 (YYMMDD.zip)
      const now = new Date();
      const year = now.getFullYear().toString().slice(-2); // 取年份后两位
      const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份补零
      const day = now.getDate().toString().padStart(2, '0'); // 日期补零
      const dateFileName = `${year}${month}${day}.zip`;

      const archivePath = path.join(buildDir, dateFileName);
      log(`创建构建产物压缩包: ${dateFileName}`);
      const archiveResult = await this.createArchive(outputDir, archivePath);
      log(`压缩包创建完成: ${archiveResult.size} 字节`);
      
      // 并行部署到每个服务器
      log(`开始并行部署到 ${projectServers.length} 个服务器`);
      const deployPromises = projectServers.map(async (server) => {
        try {
          log(`部署到服务器: ${server.name} (${server.host})`);
          const deployResult = await this.deployToServer(server, archivePath, server.deployPath);
          log(`部署到 ${server.name} 完成，备份文件: ${deployResult.archiveFileName}`);

          // 记录部署历史
          await DeploymentHistory.recordDeployment(
            projectId,
            server.id,
            deployResult.archiveFileName,
            branch
          );
          log(`已记录部署历史: ${deployResult.archiveFileName}`);

          return {
            server,
            success: true,
            backupPath: deployResult.backupPath,
            archiveFileName: deployResult.archiveFileName
          };
        } catch (error) {
          log(`部署到 ${server.name} 失败: ${error.message}`);
          return { server, success: false, error: error.message };
        }
      });
      
      // 等待所有部署完成
      const deployResults = await Promise.all(deployPromises);
      
      // 统计成功和失败的服务器
      const successCount = deployResults.filter(r => r.success).length;
      const failedCount = deployResults.filter(r => !r.success).length;

      // 对成功部署的服务器进行备份清理
      // if (successCount > 0) {
      //   log('开始清理旧备份文件...');
      //   const cleanupPromises = deployResults
      //     .filter(r => r.success)
      //     .map(async (result) => {
      //       try {
      //         const cleanupResult = await this.cleanupOldBackups(result.server, result.server.deployPath, 10);
      //         if (cleanupResult.deletedCount > 0) {
      //           log(`服务器 ${result.server.name} 清理了 ${cleanupResult.deletedCount} 个旧备份文件`);
      //         }
      //         return { server: result.server, cleanupSuccess: true };
      //       } catch (error) {
      //         log(`服务器 ${result.server.name} 清理备份失败: ${error.message}`);
      //         return { server: result.server, cleanupSuccess: false };
      //       }
      //     });

      //   await Promise.all(cleanupPromises);
      //   log('备份清理完成');
      // }

      if (failedCount > 0) {
        const failedServers = deployResults.filter(r => !r.success).map(r => r.server.name).join(', ');
        log(`部署完成，但有 ${failedCount} 个服务器部署失败: ${failedServers}`);
      } else {
        log(`部署成功完成，所有 ${successCount} 个服务器均部署成功`);
      }
      
      // 清理构建目录
      await fs.remove(buildDir);
      
      return {
        buildId,
        projectId,
        branch,
        success: failedCount === 0,
        successCount,
        failedCount,
        deployResults
      };
    } catch (error) {
      // 获取项目信息用于错误报告
      let projectName = '未知项目';
      try {
        const project = await Project.getById(projectId);
        projectName = project?.name || '未知项目';
      } catch (getProjectError) {
        // 忽略获取项目信息的错误
      }

      // 发送错误事件到前端
      socketIo.emit(`build-error-${buildId}`, {
        time: new Date().toISOString(),
        message: `部署失败: ${error.message}`,
        buildId,
        projectId,
        projectName,
        branch,
        level: 'error',
        error: {
          message: error.message,
          stack: error.stack
        }
      });

      // 发送到全局构建频道
      socketIo.emit('build-log', {
        time: new Date().toISOString(),
        message: `部署失败: ${error.message}`,
        buildId,
        projectId,
        projectName,
        branch,
        level: 'error'
      });

      // 清理构建目录
      try {
        const buildDir = path.join(this.buildsDir, buildId);
        await fs.remove(buildDir);
      } catch (cleanupError) {
        console.error('清理构建目录失败:', cleanupError.message);
      }

      throw new Error(`部署失败: ${error.message}`);
    }
  }
}

// 导出服务实例
module.exports = new DeployService();