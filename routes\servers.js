const express = require('express');
const router = express.Router();
const Server = require('../models/Server');
const ProjectServer = require('../models/ProjectServer');

// 获取所有服务器
router.get('/', async (req, res) => {
  try {
    const servers = await Server.getAll();
    // 过滤敏感数据
    const safeServers = servers.map(server => {
      const { password, ...safeServer } = server;
      return safeServer;
    });
    res.json(safeServers);
  } catch (error) {
    req.logger.error(`获取服务器列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 根据ID获取服务器
router.get('/:id', async (req, res) => {
  try {
    const server = await Server.getById(req.params.id);
    if (!server) {
      return res.status(404).json({ error: '服务器不存在' });
    }
    // 过滤敏感数据
    const { password, ...safeServer } = server;
    res.json(safeServer);
  } catch (error) {
    req.logger.error(`获取服务器详情失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 创建新服务器
router.post('/', async (req, res) => {
  try {
    const { name, host, port, username, password, description } = req.body;
    
    if (!name || !host || !username) {
      return res.status(400).json({ error: '服务器名称、主机和用户名为必填项' });
    }
    
    const server = await Server.create({
      name,
      host,
      port,
      username,
      password,
      description
    });
    
    res.status(201).json(server);
  } catch (error) {
    req.logger.error(`创建服务器失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新服务器
router.put('/:id', async (req, res) => {
  try {
    const { name, host, port, username, password, description } = req.body;
    const server = await Server.update(req.params.id, {
      name,
      host,
      port,
      username,
      password,
      description
    });
    
    res.json(server);
  } catch (error) {
    req.logger.error(`更新服务器失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 删除服务器
router.delete('/:id', async (req, res) => {
  try {
    // 删除服务器
    const result = await Server.delete(req.params.id);
    if (!result) {
      return res.status(404).json({ error: '服务器不存在' });
    }
    
    // 删除服务器与项目的关系
    await ProjectServer.removeAllServerRelationships(req.params.id);
    
    res.json({ success: true });
  } catch (error) {
    req.logger.error(`删除服务器失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目关联的服务器
router.get('/project/:projectId', async (req, res) => {
  try {
    const servers = await Server.getServersByProjectId(req.params.projectId);
    // 过滤敏感数据
    const safeServers = servers.map(server => {
      const { password, ...safeServer } = server;
      return safeServer;
    });
    res.json(safeServers);
  } catch (error) {
    req.logger.error(`获取项目服务器失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 