<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VCS凭据管理 - 前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/icons/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/animate.min.css">
  <style>
    :root {
      --primary-color: #4e73df;
      --secondary-color: #6f42c1;
      --success-color: #1cc88a;
      --info-color: #36b9cc;
      --warning-color: #f6c23e;
      --danger-color: #e74a3b;
      --light-color: #f8f9fc;
      --dark-color: #5a5c69;
      --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --transition-speed: 0.3s;
      --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      --box-shadow-hover: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    body {
      background-color: #f8f9fc;
      font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    /* 滚动条美化 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    .content-area {
      padding: 1.5rem;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .credential-card {
      border: none;
      border-radius: 0.5rem;
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      background-color: #fff;
      box-shadow: var(--card-shadow);
      transition: transform var(--transition-speed), box-shadow var(--transition-speed);
      position: relative;
      overflow: hidden;
    }
    
    .credential-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--box-shadow-hover);
    }
    
    .credential-type-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 10rem;
      font-weight: 600;
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .credential-card:hover .credential-type-badge {
      transform: scale(1.05);
    }
    
    .btn {
      border-radius: 0.35rem;
      padding: 0.375rem 0.75rem;
      font-weight: 600;
      transition: all var(--transition-speed);
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn i {
      transition: all var(--transition-speed) ease-in-out;
    }
    
    .btn:hover i {
      transform: scale(1.2);
    }
    
    .btn-group .btn {
      border-radius: 0.35rem;
      margin-right: 0.25rem;
    }
    
    .modal-content {
      border: none;
      border-radius: 0.5rem;
      box-shadow: var(--card-shadow);
    }
  </style>
</head>
<body class="animate__animated animate__fadeIn">
  <div class="container-fluid">
    <div class="row">
      <!-- 主内容区域 -->
      <main class="col-12 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
          <h1 class="h2"><i class="bi bi-key me-2 text-primary"></i>VCS凭据管理</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button class="btn btn-primary" id="add-credential-button">
              <i class="bi bi-plus-lg me-1"></i> 添加凭据
            </button>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-xl-12">
            <div class="stats-card animate__animated animate__fadeInUp">
              <div class="row">
                <div class="col-md-4">
                  <div class="stats-item">
                    <div class="icon-wrapper">
                      <i class="bi bi-key"></i>
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">总凭据数</div>
                      <div class="stats-value" id="total-credentials">加载中...</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="stats-item">
                    <div class="icon-wrapper">
                      <i class="bi bi-git"></i>
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">Git凭据</div>
                      <div class="stats-value" id="git-credentials">加载中...</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="stats-item">
                    <div class="icon-wrapper">
                      <i class="bi bi-archive"></i>
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">SVN凭据</div>
                      <div class="stats-value" id="svn-credentials">加载中...</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 凭据列表 -->
        <div class="row">
          <div class="col-12">
            <div id="credentials-container" class="animate__animated animate__fadeIn">
              <!-- 凭据卡片将在这里动态加载 -->
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 添加/编辑凭据模态框 -->
  <div class="modal fade" id="addCredentialModal" tabindex="-1" aria-labelledby="addCredentialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content animate__animated animate__fadeInUp">
        <div class="modal-header bg-light">
          <h5 class="modal-title" id="addCredentialModalLabel"><i class="bi bi-key me-2 text-primary"></i>添加VCS凭据</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="add-credential-form">
            <div class="mb-3">
              <label for="credential-name" class="form-label fw-bold">凭据名称</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-tag"></i></span>
                <input type="text" class="form-control" id="credential-name" required placeholder="输入凭据名称">
              </div>
              <div class="form-text">为这个凭据起一个易于识别的名称</div>
            </div>
            <div class="mb-3">
              <label for="credential-type" class="form-label fw-bold">版本控制系统类型</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-code-square"></i></span>
                <select class="form-select" id="credential-type" required>
                  <option value="">请选择类型</option>
                  <option value="git">Git</option>
                  <option value="svn">SVN</option>
                </select>
              </div>
            </div>
            <div class="mb-3">
              <label for="credential-username" class="form-label fw-bold">用户名</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-person"></i></span>
                <input type="text" class="form-control" id="credential-username" required placeholder="输入用户名">
              </div>
            </div>
            <div class="mb-3">
              <label for="credential-password" class="form-label fw-bold">密码</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                <input type="password" class="form-control" id="credential-password" required placeholder="输入密码">
              </div>
              <div class="form-text">密码将被加密存储</div>
            </div>
            <div class="mb-3" id="email-field" style="display: none;">
              <label for="credential-email" class="form-label fw-bold">邮箱</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                <input type="email" class="form-control" id="credential-email" placeholder="输入邮箱地址">
              </div>
              <div class="form-text">Git提交时使用的邮箱地址（可选）</div>
            </div>
            <div class="mb-3">
              <label for="credential-description" class="form-label fw-bold">描述</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-info-circle"></i></span>
                <textarea class="form-control" id="credential-description" rows="3" placeholder="输入描述信息（可选）"></textarea>
              </div>
              <div class="form-text">可选的描述信息</div>
            </div>
          </form>
        </div>
        <div class="modal-footer bg-light">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x me-1"></i>取消</button>
          <button type="button" class="btn btn-primary" id="save-credential-btn"><i class="bi bi-check2 me-1"></i>保存凭据</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 测试连接模态框 -->
  <div class="modal fade" id="testConnectionModal" tabindex="-1" aria-labelledby="testConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content animate__animated animate__fadeInUp">
        <div class="modal-header bg-light">
          <h5 class="modal-title" id="testConnectionModalLabel"><i class="bi bi-wifi me-2 text-primary"></i>测试VCS连接</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="test-connection-form">
            <div class="mb-3">
              <label for="test-repository-url" class="form-label fw-bold">仓库URL</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-link"></i></span>
                <input type="url" class="form-control" id="test-repository-url" required placeholder="输入仓库URL地址">
              </div>
              <div class="form-text">输入要测试连接的仓库URL</div>
            </div>
            <div id="test-result" class="mt-3 animate__animated animate__fadeIn" style="display: none;"></div>
          </form>
        </div>
        <div class="modal-footer bg-light">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x me-1"></i>关闭</button>
          <button type="button" class="btn btn-primary" id="test-connection-btn"><i class="bi bi-wifi me-1"></i>测试连接</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/vcs-credentials.js"></script>
</body>
</html>
